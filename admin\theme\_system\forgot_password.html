<cms:capture into='my_content' >
    <cms:if k_show_form>
        <div class="simple-box">
            <div class="panel-heading simple-heading">
                <span class="login-heading"><cms:localize 'forgot_password' /></span>
            </div>
            <div class="simple-content">
                <div class="forgot-form">

                    <form name="frm_forgotpass" action="" class="simple-form" method="post">
                        <cms:if k_message_class='error'>
                            <cms:show_error><cms:show k_message /></cms:show_error>
                        <cms:else />
                            <cms:show_info><cms:show k_message /></cms:show_info>
                        </cms:if>

                        <div class="field prepend">
                            <input id="k_user_name" name="k_user_name" autofocus="autofocus" class="text autofocus" placeholder="<cms:localize 'name_or_email' />" required="required" type="text" value=""/>
                            <cms:show_icon 'envelope-closed' />
                        </div>

                        <div class="simple-btns">
                            <button class="btn btn-primary" type="submit"><cms:show_icon 'check' /><cms:localize 'submit' /></button>
                        </div>

                        <input type="hidden" name="k_submit" value="1" />
                    </form>

                </div>
            </div>
        </div>
    <cms:else />
        <cms:if k_message_class='error'>
            <cms:show_error><cms:show k_message /></cms:show_error>
        <cms:else />
            <cms:show_info><cms:show k_message /></cms:show_info>
        </cms:if>
    </cms:if>
</cms:capture>
<cms:render 'simple' my_content />
