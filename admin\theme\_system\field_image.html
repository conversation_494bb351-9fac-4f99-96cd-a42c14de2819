<cms:if k_field_show_preview >
    <div class="img-preview">
        <a
            id="<cms:show k_field_input_id />_preview"
            class="popup-img"
            href="<cms:if k_field_value><cms:show k_field_value /><cms:else /><cms:show k_system_theme_link />includes/admin/images/camera.svg</cms:if>"
        >
            <img
                id="<cms:show k_field_input_id />_img_preview"
                name="<cms:show k_field_input_name />_img_preview"
                src="<cms:if k_field_value><cms:show k_field_value /><cms:else /><cms:show k_system_theme_link />includes/admin/images/camera.svg</cms:if>"
                <cms:if k_field_preview_width>width="<cms:show k_field_preview_width />"</cms:if>
                <cms:if k_field_preview_height>height="<cms:show k_field_preview_height />"</cms:if>
                class="k_image_preview"
            >
            <cms:show_icon 'zoom-in' />
        </a>
    </div>
<cms:else />
    <a
        class="btn popup-img"
        id="<cms:show k_field_input_id />_preview"
        href="<cms:if k_field_value><cms:show k_field_value /><cms:else /><cms:show k_system_theme_link />includes/admin/images/camera.svg</cms:if>"
    >
        <cms:show_icon 'zoom-in' />
        <cms:localize 'view_image' />
    </a>
</cms:if>

<div class="input-group upload-group"
    <cms:if k_field_input_width>style="width:<cms:sub k_field_input_width '141' />px;min-width:<cms:sub k_field_input_width '141' />px;"</cms:if>
>
    <input
        type="text"
        name="<cms:show k_field_input_name />"
        id="<cms:show k_field_input_id />"
        size="65"
        value="<cms:show k_field_value />"
        class="k_image_text input-group-field upload-path <cms:if k_field_is_deleted>readonly</cms:if>"
        <cms:if k_field_is_deleted>readonly="1"</cms:if>
    >

    <button
        class="btn popup-iframe <cms:if k_field_is_deleted>disabled</cms:if>"
        id="<cms:show k_field_input_id />_button"
        <cms:if k_field_is_deleted>disabled="1"</cms:if>
        <cms:if k_field_use_kcfinder >
            data-kc-finder="<cms:show k_field_input_id />"
            href="<cms:show k_admin_link />includes/kcfinder/browse.php?nonce=<cms:create_nonce 'kc_finder' />&type=image<cms:if k_field_kcfinder_dir>&dir=<cms:show k_field_kcfinder_dir /></cms:if>"
        <cms:else />
            href="<cms:show k_admin_link />includes/fileuploader/browser/browser.html?Type=Image&KField=<cms:show k_field_input_id />"
        </cms:if>
        type="button"
    >
        <cms:show_icon 'cloud-upload' />
        <cms:localize 'browse_server' />
    </button>

    <cms:if k_field_dynamic_insertion>
        <img src="<cms:show k_system_theme_link />assets/blank.gif" alt="" id="<cms:show k_field_input_id />_dummyimg" onload="
            var el=$('#<cms:show k_field_input_id />_button');
            if(!el.attr('idx')){
                COUCH.bindPopupImage( $('#<cms:show k_field_input_id />_preview') );

                <cms:if k_field_use_kcfinder >
                    COUCH.bindPopupIframe( el, COUCH.browseKCFinderOpen, COUCH.browseKCFinderClose, 'kcfinder-iframe' );
                <cms:else />
                    COUCH.bindPopupIframe( el );
                </cms:if>
            }
        " />
    </cms:if>
</div>

<cms:if k_add_browse_js>
    <cms:admin_add_js>
        $( function(){
            COUCH.bindPopupImage( COUCH.el.$content.find( ".popup-img" ) );

            <cms:if k_field_use_kcfinder >
                COUCH.bindPopupIframe( COUCH.el.$content.find( ".upload-group>.popup-iframe" ), COUCH.browseKCFinderOpen, COUCH.browseKCFinderClose, "kcfinder-iframe" );
            <cms:else />
                COUCH.bindPopupIframe( COUCH.el.$content.find( ".upload-group>.popup-iframe" ) );
            </cms:if>
        });

        <cms:if "<cms:not k_field_use_kcfinder />" >
            function k_browse_result( id, fileurl ){
                $("#" + id).val( fileurl ).trigger( "k_change" );
                $("#" + id).val( fileurl ).trigger( "change" );

                $("#" + id + "_preview").attr( "href", fileurl );
                $("#" + id + "_img_preview").attr( "src", fileurl );

                $.magnificPopup.close();
            }
        </cms:if>
    </cms:admin_add_js>
</cms:if>
