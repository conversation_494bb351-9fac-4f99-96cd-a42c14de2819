// Czech (cs)
plupload.addI18n({"Stop Upload":"Zastavit nahrávání","Upload URL might be wrong or doesn't exist.":"URL uploadu je možná <PERSON>n<PERSON>, nebo neexistuje.","tb":"tb","Size":"<PERSON>elikost","Close":"<PERSON>av<PERSON><PERSON>t","You must specify either browse_button or drop_element.":"Musíte specifikovat browse_button či drop_element.","Init error.":"Chyba inicializace.","Add files to the upload queue and click the start button.":"Přidejte soubory do fronty a pak spusťte nahrávání.","List":"Seznam","Filename":"Název souboru","%s specified, but cannot be found.":"%s bylo specifiko<PERSON>o, ale nebylo nalezeno.","Image format either wrong or not supported.":"Š<PERSON>tný, nebo nepodporovaný formát obrázku.","Status":"Stav","HTTP Error.":"Chyba HTTP.","Start Upload":"Spustit nahrávání","Error: File too large:":"Chyba: Soubor je příliš veliký:","kb":"kb","Duplicate file error.":"Chyba - duplikovaný soubor.","File size error.":"Chyba velikosti souboru.","N/A":"N/A","gb":"gb","Error: Invalid file extension:":"Chyba: Neplatná koncovka souboru:","Select files":"Vyberte soubory","%s already present in the queue.":"%s je již zařazen ve frontě.","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Rozlišení je mimo rozmezí! <b>%s</b> runtime podporuje obrázky pouze do %wx%hpx.","File: %s":"Soubor: %s","b":"b","Uploaded %d/%d files":"Nahráno %d/%d souborů","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload akceptuje pouze %d soubor(ů) najednou. Další soubory byly odstraněny.","%d files queued":"%d souborů ve frontě","File: %s, size: %d, max file size: %d":"Soubor: %s, velikost: %d, maximální velikost souboru: %d","Thumbnails":"Náhledy","Drag files here.":"Sem přetáhněte soubory.","Runtime ran out of available memory.":"Běh skriptu přesáhl dostupnou paměť.","File count error.":"Chyba v počtu souborů.","File extension error.":"Chyba přípony souboru.","mb":"mb","Add Files":"Přidat soubory"});
