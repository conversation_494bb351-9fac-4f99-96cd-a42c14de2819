<?php
/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2010 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This is the File Manager Connector for PHP.
 */

if ( !defined('K_COUCH_DIR') ) die(); // cannot be loaded directly

function GetFolders( $resourceType, $currentFolder )
{
	// Map the virtual path to the local server path.
	$sServerDir = ServerMapFolder( $resourceType, $currentFolder, 'GetFolders' ) ;

	// Array that will hold the folders names.
	$aFolders	= array() ;

	$oCurrentFolder = @opendir( $sServerDir ) ;

	if ($oCurrentFolder !== false)
	{
		while ( $sFile = readdir( $oCurrentFolder ) )
		{
			if ( $sFile != '.' && $sFile != '..' && is_dir( $sServerDir . $sFile ) )
				$aFolders[] = '<Folder name="' . ConvertToXmlAttribute( $sFile ) . '" />' ;
		}
		closedir( $oCurrentFolder ) ;
	}

	// Open the "Folders" node.
	echo "<Folders>" ;

	natcasesort( $aFolders ) ;
	foreach ( $aFolders as $sFolder )
		echo $sFolder ;

	// Close the "Folders" node.
	echo "</Folders>" ;
}

function GetFoldersAndFiles( $resourceType, $currentFolder )
{

	// Map the virtual path to the local server path.
	$sServerDir = ServerMapFolder( $resourceType, $currentFolder, 'GetFoldersAndFiles' ) ;

	// Arrays that will hold the folders and files names.
	$aFolders	= array() ;
	$aFiles		= array() ;

	$oCurrentFolder = @opendir( $sServerDir ) ;

	if ($oCurrentFolder !== false)
	{
		while ( $sFile = readdir( $oCurrentFolder ) )
		{
			if ( $sFile != '.' && $sFile != '..' )
			{
				if ( is_dir( $sServerDir . $sFile ) )
					$aFolders[] = '<Folder name="' . ConvertToXmlAttribute( $sFile ) . '" />' ;
				else
				{
					if( !preg_match( "/.*(-\d{1,}x\d{1,})\..*/", $sFile ) ){// skip thumbnails
					    $iFileSize = @filesize( $sServerDir . $sFile ) ;
					    if ( !$iFileSize ) {
						    $iFileSize = 0 ;
					    }
					    if ( $iFileSize > 0 )
					    {
						    $iFileSize = round( $iFileSize / 1024 ) ;
						    if ( $iFileSize < 1 )
							    $iFileSize = 1 ;
					    }

					    $aFiles[] = '<File name="' . ConvertToXmlAttribute( $sFile ) . '" size="' . $iFileSize . '" />' ;
					}
				}
			}
		}
		closedir( $oCurrentFolder ) ;
	}

	// Send the folders
	natcasesort( $aFolders ) ;
	echo '<Folders>' ;

	foreach ( $aFolders as $sFolder )
		echo $sFolder ;

	echo '</Folders>' ;

	// Send the files
	natcasesort( $aFiles ) ;
	echo '<Files>' ;

	foreach ( $aFiles as $sFiles )
		echo $sFiles ;

	echo '</Files>' ;
}

function CreateFolder( $resourceType, $currentFolder )
{
	if (!isset($_GET)) {
		global $_GET;
	}
	$sErrorNumber	= '0' ;
	$sErrorMsg		= '' ;

	if ( isset( $_GET['NewFolderName'] ) )
	{
		$sNewFolderName = $_GET['NewFolderName'] ;
		$sNewFolderName = SanitizeFolderName( $sNewFolderName ) ;

		if ( strpos( $sNewFolderName, '..' ) !== FALSE )
			$sErrorNumber = '102' ;		// Invalid folder name.
		else
		{
			// Map the virtual path to the local server path of the current folder.
			$sServerDir = ServerMapFolder( $resourceType, $currentFolder, 'CreateFolder' ) ;

			if ( is_writable( $sServerDir ) )
			{
				$sServerDir .= $sNewFolderName ;

				$sErrorMsg = CreateServerFolder( $sServerDir ) ;

				switch ( $sErrorMsg )
				{
					case '' :
						$sErrorNumber = '0' ;
						break ;
					case 'Invalid argument' :
					case 'No such file or directory' :
						$sErrorNumber = '102' ;		// Path too long.
						break ;
					default :
						$sErrorNumber = '110' ;
						break ;
				}
			}
			else
				$sErrorNumber = '103' ;
		}
	}
	else
		$sErrorNumber = '102' ;

	// Create the "Error" node.
	echo '<Error number="' . $sErrorNumber . '" />' ;
}

function FileUpload( $resourceType, $currentFolder, $sCommand )
{
	if (!isset($_FILES)) {
		global $_FILES;
	}
	$sErrorNumber = '0' ;
	$sFileName = '' ;

	if ( isset( $_FILES['NewFile'] ) && !is_null( $_FILES['NewFile']['tmp_name'] ) )
	{
		global $Config ;

		$oFile = $_FILES['NewFile'] ;

		// No POST errors in uploading?
		if( $oFile['error'] !== UPLOAD_ERR_OK ){
			$sErrorNumber = '1';
			switch( $oFile['error'] ){
				case UPLOAD_ERR_INI_SIZE:
				    $err_msg = 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
				    break;
				case UPLOAD_ERR_FORM_SIZE:
				    $err_msg = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
				    break;
				case UPLOAD_ERR_PARTIAL:
				    $err_msg = 'The uploaded file was only partially uploaded';
				    break;
				case UPLOAD_ERR_NO_FILE:
				    $err_msg = 'No file was uploaded';
				    break;
				case UPLOAD_ERR_NO_TMP_DIR:
				    $err_msg = 'Missing a temporary folder';
				    break;
				case UPLOAD_ERR_CANT_WRITE:
				    $err_msg = 'Failed to write file to disk';
				    break;
				case UPLOAD_ERR_EXTENSION:
				    $err_msg = 'File upload stopped by extension';
				    break;
				default:
				    $err_msg = 'Unknown upload error';
			}
			SendUploadResults( $sErrorNumber, '', '', $err_msg ) ;
			exit;
		}
		// Is of proper size?
		if( $Config['k_max_upload_size'] ){
			$max = ($Config['k_max_upload_size'] * 1024) * 1024;
			if( $oFile['size'] > (($Config['k_max_upload_size'] * 1024) * 1024)){
				$sErrorNumber = '1';
				$err_msg = 'File too large. Cannot be over '.$Config['k_max_upload_size'].' MB in size.';
				SendUploadResults( $sErrorNumber, '', '', $err_msg ) ;
				exit;
			}
		}

		// Map the virtual path to the local server path.
		$sServerDir = ServerMapFolder( $resourceType, $currentFolder, $sCommand ) ;

		// Get the uploaded file name.
		$sFileName = $oFile['name'] ;
		$sFileName = SanitizeFileName( $sFileName ) ;

		$sOriginalFileName = $sFileName ;

		// Get the extension.
        $sExtension = '';
        if( strrpos($sFileName, '.')!==false ){
            $sExtension = substr( $sFileName, ( strrpos($sFileName, '.') + 1 ) ) ;
            $sExtension = strtolower( $sExtension ) ;
        }
        if( $sExtension!='' ){
            if ( isset( $Config['SecureImageUploads'] ) )
            {
                if ( ( $isImageValid = IsImageValid( $oFile['tmp_name'], $sExtension ) ) === false )
                {
                    $sErrorNumber = '202' ;
                }
            }

            if ( isset( $Config['HtmlExtensions'] ) )
            {
                if ( !IsHtmlExtension( $sExtension, $Config['HtmlExtensions'] ) &&
                    ( $detectHtml = DetectHtml( $oFile['tmp_name'] ) ) === true )
                {
                    $sErrorNumber = '202' ;
                }
            }
        }

		// Check if it is an allowed extension.
		if ( $sExtension!='' && !$sErrorNumber && IsAllowedExt( $sExtension, $resourceType ) )
		{
			$iCounter = 0 ;

			while ( true )
			{
				$sFilePath = $sServerDir . $sFileName ;

				if ( is_file( $sFilePath ) )
				{
					$iCounter++ ;
					$sFileName = RemoveExtension( $sOriginalFileName ) . '-' . $iCounter . '.' . $sExtension ;
					$sErrorNumber = '201' ;
				}
				else
				{
					if ( defined('K_GALLERY_UPLOAD') ){
						$res = rename( $oFile['tmp_name'], $sFilePath ) ;
					}
					else{
						$res = move_uploaded_file( $oFile['tmp_name'], $sFilePath ) ;
					}
					if( $res===FALSE ){
						$sErrorNumber = '203' ;
						break;
					}

					if ( is_file( $sFilePath ) )
					{
						if ( isset( $Config['ChmodOnUpload'] ) && !$Config['ChmodOnUpload'] )
						{
							break ;
						}

						$permissions = 0777;

						if ( isset( $Config['ChmodOnUpload'] ) && $Config['ChmodOnUpload'] )
						{
							$permissions = $Config['ChmodOnUpload'] ;
						}

						$oldumask = umask(0) ;
						chmod( $sFilePath, $permissions ) ;
						umask( $oldumask ) ;
					}

					break ;
				}
			}

			if ( file_exists( $sFilePath ) )
			{
				//previous checks failed, try once again
				if ( isset( $isImageValid ) && $isImageValid === -1 && IsImageValid( $sFilePath, $sExtension ) === false )
				{
					@unlink( $sFilePath ) ;
					$sErrorNumber = '202' ;
				}
				else if ( isset( $detectHtml ) && $detectHtml === -1 && DetectHtml( $sFilePath ) === true )
				{
					@unlink( $sFilePath ) ;
					$sErrorNumber = '202' ;
				}
			}
		}
		else
			$sErrorNumber = '202' ;
	}
	else
		$sErrorNumber = '202' ;


	$sFileUrl = CombinePaths( GetResourceTypePath( $resourceType, $sCommand ) , $currentFolder ) ;
	$sFileUrl = CombinePaths( $sFileUrl, $sFileName ) ;

	$res = SendUploadResults( $sErrorNumber, $sFileUrl, $sFileName ) ;
	if ( defined('K_GALLERY_UPLOAD') ) return $res;
	exit ;
}
?>
