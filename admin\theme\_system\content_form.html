<cms:if "<cms:get_flash 'submit_success' />" >
    <cms:admin_add_js>
        $(function() {
            toastr.success("Your changes have been saved", "Success", { // TODO: localize
                "positionClass": "toast-bottom-right",
                "showDuration": "250",
                "hideDuration": "250",
                "timeOut": "2500",
                "extendedTimeOut": "500",
                "icon": '<cms:show_icon 'check' />'
            });
        });
    </cms:admin_add_js>
</cms:if>

<cms:set my_filters="<cms:render 'filters' />" />
<cms:if "<cms:not_empty my_filters />" >
    <div class="filter-actions">
        <cms:show my_filters />
    </div>
</cms:if>

<cms:form
    masterpage = k_selected_masterpage
    mode = k_selected_form_mode
    page_id = k_selected_page_id
    enctype = 'multipart/form-data'
    method = 'post'
    anchor = '0'
    add_security_token = '0'
    id = k_cur_form
    name = k_cur_form
    token = k_cur_token
    action = k_form_target
>

    <div class="tab-pane fade active in" id="tab-pane-<cms:show k_route_module />">

        <cms:if k_success >

            <cms:db_persist_form
                _invalidate_cache='1'
                _token=k_cur_token
            />

            <cms:if k_success >
                <cms:if k_redirect_link >
                    <cms:set_flash name='submit_success' value='1' />
                    <cms:redirect k_redirect_link />
                </cms:if>
            </cms:if>
        </cms:if>

        <cms:if k_error >
            <cms:show_error>
                <cms:each k_error >
                    <cms:show item /><br>
                </cms:each>
            </cms:show_error>
        </cms:if>

        <!-- advance settings dropdown -->
        <cms:render 'group_advanced_settings' />

        <!-- the editable regions -->
        <cms:admin_form_fields depth='1'>
            <cms:render 'form_row' />
        </cms:admin_form_fields>

        <div class="ctrl-bot">
            <cms:render 'page_actions' />
            <cms:render 'extended_actions' />

            <button class="btn tt" id="top" title="Scroll to Top" type="button"><cms:show_icon 'arrow-thick-top' /></button>
        </div>

        <input type="hidden" id="k_custom_action" name="k_custom_action" value="">
    </div>
</cms:form>
