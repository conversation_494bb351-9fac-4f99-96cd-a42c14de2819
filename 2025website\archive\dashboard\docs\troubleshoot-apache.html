<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Troubleshoot Apache Startup Problems</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_troubleshoot-apache">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/troubleshoot-apache.pdf">          Download PDF
          <span>troubleshoot-apache.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
        <aside>
          <h3>Contents</h3>
          <ol class="sections">
              <li><a href="/dashboard/docs/troubleshoot-apache.html#another_web_server_daemon_is_already_running">Another Web Server Daemon is Already Running</a></li>
          </ol>
        </aside>
      <h1>Troubleshoot Apache Startup Problems</h1>
<div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Are you having difficulty getting your Apache server started? Here is a list of common problems and their solutions.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The information in this document is based on <a href="https://community.apachefriends.org/f/viewtopic.php?f=16&amp;t=69784">this ApacheFriends community forum thread</a>.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_another_web_server_daemon_is_already_running">Another Web Server Daemon is Already Running</h2>
<div class="sectionbody">
<div class="paragraph">
<p>XAMPP displays a message like:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>Another web server daemon is already running.</pre>
</div>
</div>
<div class="paragraph">
<p>To solve this problem, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Open a new terminal window.</p>
</li>
<li>
<p>Use the lsof command to identify which other service is currently using port 80 and/or 443.</p>
<div class="literalblock">
<div class="content">
<pre>sudo lsof -i -n -P | grep TCP | grep LISTEN</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/troubleshoot-apache/image1.png" alt="image1">
</div>
</div>
</li>
<li>
<p>Terminate services currently using those ports.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>You should now be able to start Apache successfully.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If the problem persists even after performing these steps, refer to <a href="http://askubuntu.com/questions/105952/cannot-start-xampp">this source thread</a> and <a href="http://ubuntuforums.org/showthread.php?t=1192606">this forum post</a> for alternative solutions.
</td>
</tr>
</table>
</div>
</div>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
