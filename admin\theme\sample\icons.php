<?php

    if ( !defined('K_COUCH_DIR') ) die(); // cannot be loaded directly

    /*
        If this theme uses an icon-set other than Open Iconic (https://useiconic.com/open#icons),
        set the equivalent icon names below.
    */

    ///////////EDIT BELOW THIS////////////////////////////////////////

    $t['account-login'] = '';
    $t['account-logout'] = '';
    $t['action-redo'] = '';
    $t['action-undo'] = '';
    $t['align-center'] = '';
    $t['align-left'] = '';
    $t['align-right'] = '';
    $t['aperture'] = '';
    $t['arrow-bottom'] = '';
    $t['arrow-circle-bottom'] = '';
    $t['arrow-circle-left'] = '';
    $t['arrow-circle-right'] = '';
    $t['arrow-circle-top'] = '';
    $t['arrow-left'] = '';
    $t['arrow-right'] = '';
    $t['arrow-thick-bottom'] = '';
    $t['arrow-thick-left'] = '';
    $t['arrow-thick-right'] = '';
    $t['arrow-thick-top'] = '';
    $t['arrow-top'] = '';
    $t['audio-spectrum'] = '';
    $t['audio'] = '';
    $t['badge'] = '';
    $t['ban'] = '';
    $t['bar-chart'] = '';
    $t['basket'] = '';
    $t['battery-empty'] = '';
    $t['battery-full'] = '';
    $t['beaker'] = '';
    $t['bell'] = '';
    $t['bluetooth'] = '';
    $t['bold'] = '';
    $t['bolt'] = '';
    $t['book'] = '';
    $t['bookmark'] = '';
    $t['box'] = '';
    $t['briefcase'] = '';
    $t['british-pound'] = '';
    $t['browser'] = '';
    $t['brush'] = '';
    $t['bug'] = '';
    $t['bullhorn'] = '';
    $t['calculator'] = '';
    $t['calendar'] = '';
    $t['camera-slr'] = '';
    $t['caret-bottom'] = '';
    $t['caret-left'] = '';
    $t['caret-right'] = '';
    $t['caret-top'] = '';
    $t['cart'] = '';
    $t['chat'] = '';
    $t['check'] = '';
    $t['chevron-bottom'] = '';
    $t['chevron-left'] = '';
    $t['chevron-right'] = '';
    $t['chevron-top'] = '';
    $t['circle-check'] = '';
    $t['circle-x'] = '';
    $t['clipboard'] = '';
    $t['clock'] = '';
    $t['cloud-download'] = '';
    $t['cloud-upload'] = '';
    $t['cloud'] = '';
    $t['cloudy'] = '';
    $t['code'] = '';
    $t['cog'] = '';
    $t['collapse-down'] = '';
    $t['collapse-left'] = '';
    $t['collapse-right'] = '';
    $t['collapse-up'] = '';
    $t['command'] = '';
    $t['comment-square'] = '';
    $t['compass'] = '';
    $t['contrast'] = '';
    $t['copywriting'] = '';
    $t['credit-card'] = '';
    $t['crop'] = '';
    $t['dashboard'] = '';
    $t['data-transfer-download'] = '';
    $t['data-transfer-upload'] = '';
    $t['delete'] = '';
    $t['dial'] = '';
    $t['document'] = '';
    $t['dollar'] = '';
    $t['double-quote-sans-left'] = '';
    $t['double-quote-sans-right'] = '';
    $t['double-quote-serif-left'] = '';
    $t['double-quote-serif-right'] = '';
    $t['droplet'] = '';
    $t['eject'] = '';
    $t['elevator'] = '';
    $t['ellipses'] = '';
    $t['envelope-closed'] = '';
    $t['envelope-open'] = '';
    $t['euro'] = '';
    $t['excerpt'] = '';
    $t['expand-down'] = '';
    $t['expand-left'] = '';
    $t['expand-right'] = '';
    $t['expand-up'] = '';
    $t['external-link'] = '';
    $t['eye'] = '';
    $t['eyedropper'] = '';
    $t['file'] = '';
    $t['fire'] = '';
    $t['flag'] = '';
    $t['flash'] = '';
    $t['folder'] = '';
    $t['fork'] = '';
    $t['fullscreen-enter'] = '';
    $t['fullscreen-exit'] = '';
    $t['globe'] = '';
    $t['graph'] = '';
    $t['grid-four-up'] = '';
    $t['grid-three-up'] = '';
    $t['grid-two-up'] = '';
    $t['hard-drive'] = '';
    $t['header'] = '';
    $t['headphones'] = '';
    $t['heart'] = '';
    $t['home'] = '';
    $t['image'] = '';
    $t['inbox'] = '';
    $t['infinity'] = '';
    $t['info'] = '';
    $t['italic'] = '';
    $t['justify-center'] = '';
    $t['justify-left'] = '';
    $t['justify-right'] = '';
    $t['key'] = '';
    $t['laptop'] = '';
    $t['layers'] = '';
    $t['lightbulb'] = '';
    $t['link-broken'] = '';
    $t['link-intact'] = '';
    $t['list-rich'] = '';
    $t['list'] = '';
    $t['location'] = '';
    $t['lock-locked'] = '';
    $t['lock-unlocked'] = '';
    $t['loop-circular'] = '';
    $t['loop-square'] = '';
    $t['loop'] = '';
    $t['magnifying-glass'] = '';
    $t['map-marker'] = '';
    $t['map'] = '';
    $t['media-pause'] = '';
    $t['media-play'] = '';
    $t['media-record'] = '';
    $t['media-skip-backward'] = '';
    $t['media-skip-forward'] = '';
    $t['media-step-backward'] = '';
    $t['media-step-forward'] = '';
    $t['media-stop'] = '';
    $t['medical-cross'] = '';
    $t['menu'] = '';
    $t['microphone'] = '';
    $t['minus'] = '';
    $t['monitor'] = '';
    $t['moon'] = '';
    $t['move'] = '';
    $t['musical-note'] = '';
    $t['paperclip'] = '';
    $t['pencil'] = '';
    $t['people'] = '';
    $t['person'] = '';
    $t['phone'] = '';
    $t['pie-chart'] = '';
    $t['pin'] = '';
    $t['play-circle'] = '';
    $t['plus'] = '';
    $t['power-standby'] = '';
    $t['print'] = '';
    $t['project'] = '';
    $t['pulse'] = '';
    $t['puzzle-piece'] = '';
    $t['question-mark'] = '';
    $t['rain'] = '';
    $t['random'] = '';
    $t['reload'] = '';
    $t['resize-both'] = '';
    $t['resize-height'] = '';
    $t['resize-width'] = '';
    $t['rss-alt'] = '';
    $t['rss'] = '';
    $t['script'] = '';
    $t['share-boxed'] = '';
    $t['share'] = '';
    $t['shield'] = '';
    $t['signal'] = '';
    $t['signpost'] = '';
    $t['sort-ascending'] = '';
    $t['sort-descending'] = '';
    $t['spreadsheet'] = '';
    $t['star'] = '';
    $t['sun'] = '';
    $t['tablet'] = '';
    $t['tag'] = '';
    $t['tags'] = '';
    $t['target'] = '';
    $t['task'] = '';
    $t['terminal'] = '';
    $t['text'] = '';
    $t['thumb-down'] = '';
    $t['thumb-up'] = '';
    $t['timer'] = '';
    $t['transfer'] = '';
    $t['trash'] = '';
    $t['underline'] = '';
    $t['vertical-align-bottom'] = '';
    $t['vertical-align-center'] = '';
    $t['vertical-align-top'] = '';
    $t['video'] = '';
    $t['volume-high'] = '';
    $t['volume-low'] = '';
    $t['volume-off'] = '';
    $t['warning'] = '';
    $t['wifi'] = '';
    $t['wrench'] = '';
    $t['x'] = '';
    $t['yen'] = '';
    $t['zoom-in'] = '';
    $t['zoom-out'] = '';
