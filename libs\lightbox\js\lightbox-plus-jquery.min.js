/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */
!function(a,b){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=a.document?b(a,!0):function(a){if(!a.document)throw new Error("jQuery requires a window with a document");return b(a)}:b(a)}("undefined"!=typeof window?window:this,function(a,b){"use strict";function c(a,b,c){c=c||ua;var d,e,f=c.createElement("script");if(f.text=a,b)for(d in va)(e=b[d]||b.getAttribute&&b.getAttribute(d))&&f.setAttribute(d,e);c.head.appendChild(f).parentNode.removeChild(f)}function d(a){return null==a?a+"":"object"==typeof a||"function"==typeof a?ma[na.call(a)]||"object":typeof a}function e(a){var b=!!a&&"length"in a&&a.length,c=d(a);return!sa(a)&&!ta(a)&&("array"===c||0===b||"number"==typeof b&&b>0&&b-1 in a)}function f(a,b){return a.nodeName&&a.nodeName.toLowerCase()===b.toLowerCase()}function g(a,b){return b?"\0"===a?"�":a.slice(0,-1)+"\\"+a.charCodeAt(a.length-1).toString(16)+" ":"\\"+a}function h(a,b,c){return sa(b)?ya.grep(a,function(a,d){return!!b.call(a,d,a)!==c}):b.nodeType?ya.grep(a,function(a){return a===b!==c}):"string"!=typeof b?ya.grep(a,function(a){return la.call(b,a)>-1!==c}):ya.filter(b,a,c)}function i(a,b){for(;(a=a[b])&&1!==a.nodeType;);return a}function j(a){var b={};return ya.each(a.match(Pa)||[],function(a,c){b[c]=!0}),b}function k(a){return a}function l(a){throw a}function m(a,b,c,d){var e;try{a&&sa(e=a.promise)?e.call(a).done(b).fail(c):a&&sa(e=a.then)?e.call(a,b,c):b.apply(void 0,[a].slice(d))}catch(a){c.apply(void 0,[a])}}function n(){ua.removeEventListener("DOMContentLoaded",n),a.removeEventListener("load",n),ya.ready()}function o(a,b){return b.toUpperCase()}function p(a){return a.replace(Ta,"ms-").replace(Ua,o)}function q(){this.expando=ya.expando+q.uid++}function r(a){return"true"===a||"false"!==a&&("null"===a?null:a===+a+""?+a:Ya.test(a)?JSON.parse(a):a)}function s(a,b,c){var d;if(void 0===c&&1===a.nodeType)if(d="data-"+b.replace(Za,"-$&").toLowerCase(),"string"==typeof(c=a.getAttribute(d))){try{c=r(c)}catch(a){}Xa.set(a,b,c)}else c=void 0;return c}function t(a,b,c,d){var e,f,g=20,h=d?function(){return d.cur()}:function(){return ya.css(a,b,"")},i=h(),j=c&&c[3]||(ya.cssNumber[b]?"":"px"),k=a.nodeType&&(ya.cssNumber[b]||"px"!==j&&+i)&&_a.exec(ya.css(a,b));if(k&&k[3]!==j){for(i/=2,j=j||k[3],k=+i||1;g--;)ya.style(a,b,k+j),(1-f)*(1-(f=h()/i||.5))<=0&&(g=0),k/=f;k*=2,ya.style(a,b,k+j),c=c||[]}return c&&(k=+k||+i||0,e=c[1]?k+(c[1]+1)*c[2]:+c[2],d&&(d.unit=j,d.start=k,d.end=e)),e}function u(a){var b,c=a.ownerDocument,d=a.nodeName,e=fb[d];return e||(b=c.body.appendChild(c.createElement(d)),e=ya.css(b,"display"),b.parentNode.removeChild(b),"none"===e&&(e="block"),fb[d]=e,e)}function v(a,b){for(var c,d,e=[],f=0,g=a.length;f<g;f++)d=a[f],d.style&&(c=d.style.display,b?("none"===c&&(e[f]=Wa.get(d,"display")||null,e[f]||(d.style.display="")),""===d.style.display&&eb(d)&&(e[f]=u(d))):"none"!==c&&(e[f]="none",Wa.set(d,"display",c)));for(f=0;f<g;f++)null!=e[f]&&(a[f].style.display=e[f]);return a}function w(a,b){var c;return c=void 0!==a.getElementsByTagName?a.getElementsByTagName(b||"*"):void 0!==a.querySelectorAll?a.querySelectorAll(b||"*"):[],void 0===b||b&&f(a,b)?ya.merge([a],c):c}function x(a,b){for(var c=0,d=a.length;c<d;c++)Wa.set(a[c],"globalEval",!b||Wa.get(b[c],"globalEval"))}function y(a,b,c,e,f){for(var g,h,i,j,k,l,m=b.createDocumentFragment(),n=[],o=0,p=a.length;o<p;o++)if((g=a[o])||0===g)if("object"===d(g))ya.merge(n,g.nodeType?[g]:g);else if(kb.test(g)){for(h=h||m.appendChild(b.createElement("div")),i=(hb.exec(g)||["",""])[1].toLowerCase(),j=jb[i]||jb._default,h.innerHTML=j[1]+ya.htmlPrefilter(g)+j[2],l=j[0];l--;)h=h.lastChild;ya.merge(n,h.childNodes),h=m.firstChild,h.textContent=""}else n.push(b.createTextNode(g));for(m.textContent="",o=0;g=n[o++];)if(e&&ya.inArray(g,e)>-1)f&&f.push(g);else if(k=cb(g),h=w(m.appendChild(g),"script"),k&&x(h),c)for(l=0;g=h[l++];)ib.test(g.type||"")&&c.push(g);return m}function z(){return!0}function A(){return!1}function B(a,b,c,d,e,f){var g,h;if("object"==typeof b){"string"!=typeof c&&(d=d||c,c=void 0);for(h in b)B(a,h,c,d,b[h],f);return a}if(null==d&&null==e?(e=c,d=c=void 0):null==e&&("string"==typeof c?(e=d,d=void 0):(e=d,d=c,c=void 0)),!1===e)e=A;else if(!e)return a;return 1===f&&(g=e,e=function(a){return ya().off(a),g.apply(this,arguments)},e.guid=g.guid||(g.guid=ya.guid++)),a.each(function(){ya.event.add(this,b,e,d,c)})}function C(a,b,c){if(!c)return void(void 0===Wa.get(a,b)&&ya.event.add(a,b,z));Wa.set(a,b,!1),ya.event.add(a,b,{namespace:!1,handler:function(a){var c,d=Wa.get(this,b);if(1&a.isTrigger&&this[b]){if(d)(ya.event.special[b]||{}).delegateType&&a.stopPropagation();else if(d=ia.call(arguments),Wa.set(this,b,d),this[b](),c=Wa.get(this,b),Wa.set(this,b,!1),d!==c)return a.stopImmediatePropagation(),a.preventDefault(),c}else d&&(Wa.set(this,b,ya.event.trigger(d[0],d.slice(1),this)),a.stopPropagation(),a.isImmediatePropagationStopped=z)}})}function D(a,b){return f(a,"table")&&f(11!==b.nodeType?b:b.firstChild,"tr")?ya(a).children("tbody")[0]||a:a}function E(a){return a.type=(null!==a.getAttribute("type"))+"/"+a.type,a}function F(a){return"true/"===(a.type||"").slice(0,5)?a.type=a.type.slice(5):a.removeAttribute("type"),a}function G(a,b){var c,d,e,f,g,h,i;if(1===b.nodeType){if(Wa.hasData(a)&&(f=Wa.get(a),i=f.events)){Wa.remove(b,"handle events");for(e in i)for(c=0,d=i[e].length;c<d;c++)ya.event.add(b,e,i[e][c])}Xa.hasData(a)&&(g=Xa.access(a),h=ya.extend({},g),Xa.set(b,h))}}function H(a,b){var c=b.nodeName.toLowerCase();"input"===c&&gb.test(a.type)?b.checked=a.checked:"input"!==c&&"textarea"!==c||(b.defaultValue=a.defaultValue)}function I(a,b,d,e){b=ja(b);var f,g,h,i,j,k,l=0,m=a.length,n=m-1,o=b[0],p=sa(o);if(p||m>1&&"string"==typeof o&&!ra.checkClone&&nb.test(o))return a.each(function(c){var f=a.eq(c);p&&(b[0]=o.call(this,c,f.html())),I(f,b,d,e)});if(m&&(f=y(b,a[0].ownerDocument,!1,a,e),g=f.firstChild,1===f.childNodes.length&&(f=g),g||e)){for(h=ya.map(w(f,"script"),E),i=h.length;l<m;l++)j=f,l!==n&&(j=ya.clone(j,!0,!0),i&&ya.merge(h,w(j,"script"))),d.call(a[l],j,l);if(i)for(k=h[h.length-1].ownerDocument,ya.map(h,F),l=0;l<i;l++)j=h[l],ib.test(j.type||"")&&!Wa.access(j,"globalEval")&&ya.contains(k,j)&&(j.src&&"module"!==(j.type||"").toLowerCase()?ya._evalUrl&&!j.noModule&&ya._evalUrl(j.src,{nonce:j.nonce||j.getAttribute("nonce")},k):c(j.textContent.replace(ob,""),j,k))}return a}function J(a,b,c){for(var d,e=b?ya.filter(b,a):a,f=0;null!=(d=e[f]);f++)c||1!==d.nodeType||ya.cleanData(w(d)),d.parentNode&&(c&&cb(d)&&x(w(d,"script")),d.parentNode.removeChild(d));return a}function K(a,b,c){var d,e,f,g,h=qb.test(b),i=a.style;return c=c||rb(a),c&&(g=c.getPropertyValue(b)||c[b],h&&g&&(g=g.replace(Da,"$1")||void 0),""!==g||cb(a)||(g=ya.style(a,b)),!ra.pixelBoxStyles()&&pb.test(g)&&tb.test(b)&&(d=i.width,e=i.minWidth,f=i.maxWidth,i.minWidth=i.maxWidth=i.width=g,g=c.width,i.width=d,i.minWidth=e,i.maxWidth=f)),void 0!==g?g+"":g}function L(a,b){return{get:function(){return a()?void delete this.get:(this.get=b).apply(this,arguments)}}}function M(a){for(var b=a[0].toUpperCase()+a.slice(1),c=ub.length;c--;)if((a=ub[c]+b)in vb)return a}function N(a){var b=ya.cssProps[a]||wb[a];return b||(a in vb?a:wb[a]=M(a)||a)}function O(a,b,c){var d=_a.exec(b);return d?Math.max(0,d[2]-(c||0))+(d[3]||"px"):b}function P(a,b,c,d,e,f){var g="width"===b?1:0,h=0,i=0,j=0;if(c===(d?"border":"content"))return 0;for(;g<4;g+=2)"margin"===c&&(j+=ya.css(a,c+ab[g],!0,e)),d?("content"===c&&(i-=ya.css(a,"padding"+ab[g],!0,e)),"margin"!==c&&(i-=ya.css(a,"border"+ab[g]+"Width",!0,e))):(i+=ya.css(a,"padding"+ab[g],!0,e),"padding"!==c?i+=ya.css(a,"border"+ab[g]+"Width",!0,e):h+=ya.css(a,"border"+ab[g]+"Width",!0,e));return!d&&f>=0&&(i+=Math.max(0,Math.ceil(a["offset"+b[0].toUpperCase()+b.slice(1)]-f-i-h-.5))||0),i+j}function Q(a,b,c){var d=rb(a),e=!ra.boxSizingReliable()||c,g=e&&"border-box"===ya.css(a,"boxSizing",!1,d),h=g,i=K(a,b,d),j="offset"+b[0].toUpperCase()+b.slice(1);if(pb.test(i)){if(!c)return i;i="auto"}return(!ra.boxSizingReliable()&&g||!ra.reliableTrDimensions()&&f(a,"tr")||"auto"===i||!parseFloat(i)&&"inline"===ya.css(a,"display",!1,d))&&a.getClientRects().length&&(g="border-box"===ya.css(a,"boxSizing",!1,d),(h=j in a)&&(i=a[j])),(i=parseFloat(i)||0)+P(a,b,c||(g?"border":"content"),h,d,i)+"px"}function R(a,b,c,d,e){return new R.prototype.init(a,b,c,d,e)}function S(){Bb&&(!1===ua.hidden&&a.requestAnimationFrame?a.requestAnimationFrame(S):a.setTimeout(S,ya.fx.interval),ya.fx.tick())}function T(){return a.setTimeout(function(){Ab=void 0}),Ab=Date.now()}function U(a,b){var c,d=0,e={height:a};for(b=b?1:0;d<4;d+=2-b)c=ab[d],e["margin"+c]=e["padding"+c]=a;return b&&(e.opacity=e.width=a),e}function V(a,b,c){for(var d,e=(Y.tweeners[b]||[]).concat(Y.tweeners["*"]),f=0,g=e.length;f<g;f++)if(d=e[f].call(c,b,a))return d}function W(a,b,c){var d,e,f,g,h,i,j,k,l="width"in b||"height"in b,m=this,n={},o=a.style,p=a.nodeType&&eb(a),q=Wa.get(a,"fxshow");c.queue||(g=ya._queueHooks(a,"fx"),null==g.unqueued&&(g.unqueued=0,h=g.empty.fire,g.empty.fire=function(){g.unqueued||h()}),g.unqueued++,m.always(function(){m.always(function(){g.unqueued--,ya.queue(a,"fx").length||g.empty.fire()})}));for(d in b)if(e=b[d],Cb.test(e)){if(delete b[d],f=f||"toggle"===e,e===(p?"hide":"show")){if("show"!==e||!q||void 0===q[d])continue;p=!0}n[d]=q&&q[d]||ya.style(a,d)}if((i=!ya.isEmptyObject(b))||!ya.isEmptyObject(n)){l&&1===a.nodeType&&(c.overflow=[o.overflow,o.overflowX,o.overflowY],j=q&&q.display,null==j&&(j=Wa.get(a,"display")),k=ya.css(a,"display"),"none"===k&&(j?k=j:(v([a],!0),j=a.style.display||j,k=ya.css(a,"display"),v([a]))),("inline"===k||"inline-block"===k&&null!=j)&&"none"===ya.css(a,"float")&&(i||(m.done(function(){o.display=j}),null==j&&(k=o.display,j="none"===k?"":k)),o.display="inline-block")),c.overflow&&(o.overflow="hidden",m.always(function(){o.overflow=c.overflow[0],o.overflowX=c.overflow[1],o.overflowY=c.overflow[2]})),i=!1;for(d in n)i||(q?"hidden"in q&&(p=q.hidden):q=Wa.access(a,"fxshow",{display:j}),f&&(q.hidden=!p),p&&v([a],!0),m.done(function(){p||v([a]),Wa.remove(a,"fxshow");for(d in n)ya.style(a,d,n[d])})),i=V(p?q[d]:0,d,m),d in q||(q[d]=i.start,p&&(i.end=i.start,i.start=0))}}function X(a,b){var c,d,e,f,g;for(c in a)if(d=p(c),e=b[d],f=a[c],Array.isArray(f)&&(e=f[1],f=a[c]=f[0]),c!==d&&(a[d]=f,delete a[c]),(g=ya.cssHooks[d])&&"expand"in g){f=g.expand(f),delete a[d];for(c in f)c in a||(a[c]=f[c],b[c]=e)}else b[d]=e}function Y(a,b,c){var d,e,f=0,g=Y.prefilters.length,h=ya.Deferred().always(function(){delete i.elem}),i=function(){if(e)return!1;for(var b=Ab||T(),c=Math.max(0,j.startTime+j.duration-b),d=c/j.duration||0,f=1-d,g=0,i=j.tweens.length;g<i;g++)j.tweens[g].run(f);return h.notifyWith(a,[j,f,c]),f<1&&i?c:(i||h.notifyWith(a,[j,1,0]),h.resolveWith(a,[j]),!1)},j=h.promise({elem:a,props:ya.extend({},b),opts:ya.extend(!0,{specialEasing:{},easing:ya.easing._default},c),originalProperties:b,originalOptions:c,startTime:Ab||T(),duration:c.duration,tweens:[],createTween:function(b,c){var d=ya.Tween(a,j.opts,b,c,j.opts.specialEasing[b]||j.opts.easing);return j.tweens.push(d),d},stop:function(b){var c=0,d=b?j.tweens.length:0;if(e)return this;for(e=!0;c<d;c++)j.tweens[c].run(1);return b?(h.notifyWith(a,[j,1,0]),h.resolveWith(a,[j,b])):h.rejectWith(a,[j,b]),this}}),k=j.props;for(X(k,j.opts.specialEasing);f<g;f++)if(d=Y.prefilters[f].call(j,a,k,j.opts))return sa(d.stop)&&(ya._queueHooks(j.elem,j.opts.queue).stop=d.stop.bind(d)),d;return ya.map(k,V,j),sa(j.opts.start)&&j.opts.start.call(a,j),j.progress(j.opts.progress).done(j.opts.done,j.opts.complete).fail(j.opts.fail).always(j.opts.always),ya.fx.timer(ya.extend(i,{elem:a,anim:j,queue:j.opts.queue})),j}function Z(a){return(a.match(Pa)||[]).join(" ")}function $(a){return a.getAttribute&&a.getAttribute("class")||""}function _(a){return Array.isArray(a)?a:"string"==typeof a?a.match(Pa)||[]:[]}function aa(a,b,c,e){var f;if(Array.isArray(b))ya.each(b,function(b,d){c||Ob.test(a)?e(a,d):aa(a+"["+("object"==typeof d&&null!=d?b:"")+"]",d,c,e)});else if(c||"object"!==d(b))e(a,b);else for(f in b)aa(a+"["+f+"]",b[f],c,e)}function ba(a){return function(b,c){"string"!=typeof b&&(c=b,b="*");var d,e=0,f=b.toLowerCase().match(Pa)||[];if(sa(c))for(;d=f[e++];)"+"===d[0]?(d=d.slice(1)||"*",(a[d]=a[d]||[]).unshift(c)):(a[d]=a[d]||[]).push(c)}}function ca(a,b,c,d){function e(h){var i;return f[h]=!0,ya.each(a[h]||[],function(a,h){var j=h(b,c,d);return"string"!=typeof j||g||f[j]?g?!(i=j):void 0:(b.dataTypes.unshift(j),e(j),!1)}),i}var f={},g=a===$b;return e(b.dataTypes[0])||!f["*"]&&e("*")}function da(a,b){var c,d,e=ya.ajaxSettings.flatOptions||{};for(c in b)void 0!==b[c]&&((e[c]?a:d||(d={}))[c]=b[c]);return d&&ya.extend(!0,a,d),a}function ea(a,b,c){for(var d,e,f,g,h=a.contents,i=a.dataTypes;"*"===i[0];)i.shift(),void 0===d&&(d=a.mimeType||b.getResponseHeader("Content-Type"));if(d)for(e in h)if(h[e]&&h[e].test(d)){i.unshift(e);break}if(i[0]in c)f=i[0];else{for(e in c){if(!i[0]||a.converters[e+" "+i[0]]){f=e;break}g||(g=e)}f=f||g}if(f)return f!==i[0]&&i.unshift(f),c[f]}function fa(a,b,c,d){var e,f,g,h,i,j={},k=a.dataTypes.slice();if(k[1])for(g in a.converters)j[g.toLowerCase()]=a.converters[g];for(f=k.shift();f;)if(a.responseFields[f]&&(c[a.responseFields[f]]=b),!i&&d&&a.dataFilter&&(b=a.dataFilter(b,a.dataType)),i=f,f=k.shift())if("*"===f)f=i;else if("*"!==i&&i!==f){if(!(g=j[i+" "+f]||j["* "+f]))for(e in j)if(h=e.split(" "),h[1]===f&&(g=j[i+" "+h[0]]||j["* "+h[0]])){!0===g?g=j[e]:!0!==j[e]&&(f=h[0],k.unshift(h[1]));break}if(!0!==g)if(g&&a.throws)b=g(b);else try{b=g(b)}catch(a){return{state:"parsererror",error:g?a:"No conversion from "+i+" to "+f}}}return{state:"success",data:b}}var ga=[],ha=Object.getPrototypeOf,ia=ga.slice,ja=ga.flat?function(a){return ga.flat.call(a)}:function(a){return ga.concat.apply([],a)},ka=ga.push,la=ga.indexOf,ma={},na=ma.toString,oa=ma.hasOwnProperty,pa=oa.toString,qa=pa.call(Object),ra={},sa=function(a){return"function"==typeof a&&"number"!=typeof a.nodeType&&"function"!=typeof a.item},ta=function(a){return null!=a&&a===a.window},ua=a.document,va={type:!0,src:!0,nonce:!0,noModule:!0},wa="3.7.1",xa=/HTML$/i,ya=function(a,b){return new ya.fn.init(a,b)};ya.fn=ya.prototype={jquery:wa,constructor:ya,length:0,toArray:function(){return ia.call(this)},get:function(a){return null==a?ia.call(this):a<0?this[a+this.length]:this[a]},pushStack:function(a){var b=ya.merge(this.constructor(),a);return b.prevObject=this,b},each:function(a){return ya.each(this,a)},map:function(a){return this.pushStack(ya.map(this,function(b,c){return a.call(b,c,b)}))},slice:function(){return this.pushStack(ia.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(ya.grep(this,function(a,b){return(b+1)%2}))},odd:function(){return this.pushStack(ya.grep(this,function(a,b){return b%2}))},eq:function(a){var b=this.length,c=+a+(a<0?b:0);return this.pushStack(c>=0&&c<b?[this[c]]:[])},end:function(){return this.prevObject||this.constructor()},push:ka,sort:ga.sort,splice:ga.splice},ya.extend=ya.fn.extend=function(){var a,b,c,d,e,f,g=arguments[0]||{},h=1,i=arguments.length,j=!1;for("boolean"==typeof g&&(j=g,g=arguments[h]||{},h++),"object"==typeof g||sa(g)||(g={}),h===i&&(g=this,h--);h<i;h++)if(null!=(a=arguments[h]))for(b in a)d=a[b],"__proto__"!==b&&g!==d&&(j&&d&&(ya.isPlainObject(d)||(e=Array.isArray(d)))?(c=g[b],f=e&&!Array.isArray(c)?[]:e||ya.isPlainObject(c)?c:{},e=!1,g[b]=ya.extend(j,f,d)):void 0!==d&&(g[b]=d));return g},ya.extend({expando:"jQuery"+(wa+Math.random()).replace(/\D/g,""),isReady:!0,error:function(a){throw new Error(a)},noop:function(){},isPlainObject:function(a){var b,c;return!(!a||"[object Object]"!==na.call(a))&&(!(b=ha(a))||"function"==typeof(c=oa.call(b,"constructor")&&b.constructor)&&pa.call(c)===qa)},isEmptyObject:function(a){var b;for(b in a)return!1;return!0},globalEval:function(a,b,d){c(a,{nonce:b&&b.nonce},d)},each:function(a,b){var c,d=0;if(e(a))for(c=a.length;d<c&&!1!==b.call(a[d],d,a[d]);d++);else for(d in a)if(!1===b.call(a[d],d,a[d]))break;return a},text:function(a){var b,c="",d=0,e=a.nodeType;if(!e)for(;b=a[d++];)c+=ya.text(b);return 1===e||11===e?a.textContent:9===e?a.documentElement.textContent:3===e||4===e?a.nodeValue:c},makeArray:function(a,b){var c=b||[];return null!=a&&(e(Object(a))?ya.merge(c,"string"==typeof a?[a]:a):ka.call(c,a)),c},inArray:function(a,b,c){return null==b?-1:la.call(b,a,c)},isXMLDoc:function(a){var b=a&&a.namespaceURI,c=a&&(a.ownerDocument||a).documentElement;return!xa.test(b||c&&c.nodeName||"HTML")},merge:function(a,b){for(var c=+b.length,d=0,e=a.length;d<c;d++)a[e++]=b[d];return a.length=e,a},grep:function(a,b,c){for(var d=[],e=0,f=a.length,g=!c;e<f;e++)!b(a[e],e)!==g&&d.push(a[e]);return d},map:function(a,b,c){var d,f,g=0,h=[];if(e(a))for(d=a.length;g<d;g++)null!=(f=b(a[g],g,c))&&h.push(f);else for(g in a)null!=(f=b(a[g],g,c))&&h.push(f);return ja(h)},guid:1,support:ra}),"function"==typeof Symbol&&(ya.fn[Symbol.iterator]=ga[Symbol.iterator]),ya.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(a,b){ma["[object "+b+"]"]=b.toLowerCase()});var za=ga.pop,Aa=ga.sort,Ba=ga.splice,Ca="[\\x20\\t\\r\\n\\f]",Da=new RegExp("^"+Ca+"+|((?:^|[^\\\\])(?:\\\\.)*)"+Ca+"+$","g");ya.contains=function(a,b){var c=b&&b.parentNode;return a===c||!(!c||1!==c.nodeType||!(a.contains?a.contains(c):a.compareDocumentPosition&&16&a.compareDocumentPosition(c)))};var Ea=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;ya.escapeSelector=function(a){return(a+"").replace(Ea,g)};var Fa=ua,Ga=ka;!function(){function b(){try{return E.activeElement}catch(a){}}function c(a,b,d,e){var f,g,h,i,j,k,n,q=b&&b.ownerDocument,r=b?b.nodeType:9;if(d=d||[],"string"!=typeof a||!a||1!==r&&9!==r&&11!==r)return d;if(!e&&(m(b),b=b||E,G)){if(11!==r&&(j=da.exec(a)))if(f=j[1]){if(9===r){if(!(h=b.getElementById(f)))return d;if(h.id===f)return J.call(d,h),d}else if(q&&(h=q.getElementById(f))&&c.contains(b,h)&&h.id===f)return J.call(d,h),d}else{if(j[2])return J.apply(d,b.getElementsByTagName(a)),d;if((f=j[3])&&b.getElementsByClassName)return J.apply(d,b.getElementsByClassName(f)),d}if(!(Q[a+" "]||H&&H.test(a))){if(n=a,q=b,1===r&&(Z.test(a)||Y.test(a))){for(q=ea.test(a)&&l(b.parentNode)||b,q==b&&ra.scope||((i=b.getAttribute("id"))?i=ya.escapeSelector(i):b.setAttribute("id",i=K)),k=o(a),g=k.length;g--;)k[g]=(i?"#"+i:":scope")+" "+p(k[g]);n=k.join(",")}try{return J.apply(d,q.querySelectorAll(n)),d}catch(b){Q(a,!0)}finally{i===K&&b.removeAttribute("id")}}}return y(a.replace(Da,"$1"),b,d,e)}function d(){function a(c,d){return b.push(c+" ")>A.cacheLength&&delete a[b.shift()],a[c+" "]=d}var b=[];return a}function e(a){return a[K]=!0,a}function g(a){var b=E.createElement("fieldset");try{return!!a(b)}catch(a){return!1}finally{b.parentNode&&b.parentNode.removeChild(b),b=null}}function h(a){return function(b){return f(b,"input")&&b.type===a}}function i(a){return function(b){return(f(b,"input")||f(b,"button"))&&b.type===a}}function j(a){return function(b){return"form"in b?b.parentNode&&!1===b.disabled?"label"in b?"label"in b.parentNode?b.parentNode.disabled===a:b.disabled===a:b.isDisabled===a||b.isDisabled!==!a&&ka(b)===a:b.disabled===a:"label"in b&&b.disabled===a}}function k(a){return e(function(b){return b=+b,e(function(c,d){for(var e,f=a([],c.length,b),g=f.length;g--;)c[e=f[g]]&&(c[e]=!(d[e]=c[e]))})})}function l(a){return a&&void 0!==a.getElementsByTagName&&a}function m(a){var b,d=a?a.ownerDocument||a:Fa;return d!=E&&9===d.nodeType&&d.documentElement?(E=d,F=E.documentElement,G=!ya.isXMLDoc(E),I=F.matches||F.webkitMatchesSelector||F.msMatchesSelector,F.msMatchesSelector&&Fa!=E&&(b=E.defaultView)&&b.top!==b&&b.addEventListener("unload",ja),ra.getById=g(function(a){return F.appendChild(a).id=ya.expando,!E.getElementsByName||!E.getElementsByName(ya.expando).length}),ra.disconnectedMatch=g(function(a){return I.call(a,"*")}),ra.scope=g(function(){return E.querySelectorAll(":scope")}),ra.cssHas=g(function(){try{return E.querySelector(":has(*,:jqfake)"),!1}catch(a){return!0}}),ra.getById?(A.filter.ID=function(a){var b=a.replace(fa,ha);return function(a){return a.getAttribute("id")===b}},A.find.ID=function(a,b){if(void 0!==b.getElementById&&G){var c=b.getElementById(a);return c?[c]:[]}}):(A.filter.ID=function(a){var b=a.replace(fa,ha);return function(a){var c=void 0!==a.getAttributeNode&&a.getAttributeNode("id");return c&&c.value===b}},A.find.ID=function(a,b){if(void 0!==b.getElementById&&G){var c,d,e,f=b.getElementById(a);if(f){if((c=f.getAttributeNode("id"))&&c.value===a)return[f];for(e=b.getElementsByName(a),d=0;f=e[d++];)if((c=f.getAttributeNode("id"))&&c.value===a)return[f]}return[]}}),A.find.TAG=function(a,b){return void 0!==b.getElementsByTagName?b.getElementsByTagName(a):b.querySelectorAll(a)},A.find.CLASS=function(a,b){if(void 0!==b.getElementsByClassName&&G)return b.getElementsByClassName(a)},H=[],g(function(a){var b;F.appendChild(a).innerHTML="<a id='"+K+"' href='' disabled='disabled'></a><select id='"+K+"-\r\\' disabled='disabled'><option selected=''></option></select>",a.querySelectorAll("[selected]").length||H.push("\\["+Ca+"*(?:value|"+S+")"),a.querySelectorAll("[id~="+K+"-]").length||H.push("~="),a.querySelectorAll("a#"+K+"+*").length||H.push(".#.+[+~]"),a.querySelectorAll(":checked").length||H.push(":checked"),b=E.createElement("input"),b.setAttribute("type","hidden"),a.appendChild(b).setAttribute("name","D"),F.appendChild(a).disabled=!0,2!==a.querySelectorAll(":disabled").length&&H.push(":enabled",":disabled"),b=E.createElement("input"),b.setAttribute("name",""),a.appendChild(b),a.querySelectorAll("[name='']").length||H.push("\\["+Ca+"*name"+Ca+"*="+Ca+"*(?:''|\"\")")}),ra.cssHas||H.push(":has"),H=H.length&&new RegExp(H.join("|")),R=function(a,b){if(a===b)return D=!0,0;var d=!a.compareDocumentPosition-!b.compareDocumentPosition;return d||(d=(a.ownerDocument||a)==(b.ownerDocument||b)?a.compareDocumentPosition(b):1,1&d||!ra.sortDetached&&b.compareDocumentPosition(a)===d?a===E||a.ownerDocument==Fa&&c.contains(Fa,a)?-1:b===E||b.ownerDocument==Fa&&c.contains(Fa,b)?1:C?la.call(C,a)-la.call(C,b):0:4&d?-1:1)},E):E}function n(){}function o(a,b){var d,e,f,g,h,i,j,k=O[a+" "];if(k)return b?0:k.slice(0);for(h=a,i=[],j=A.preFilter;h;){d&&!(e=X.exec(h))||(e&&(h=h.slice(e[0].length)||h),i.push(f=[])),d=!1,(e=Y.exec(h))&&(d=e.shift(),f.push({value:d,type:e[0].replace(Da," ")}),h=h.slice(d.length));for(g in A.filter)!(e=aa[g].exec(h))||j[g]&&!(e=j[g](e))||(d=e.shift(),f.push({value:d,type:g,matches:e}),h=h.slice(d.length));if(!d)break}return b?h.length:h?c.error(a):O(a,i).slice(0)}function p(a){for(var b=0,c=a.length,d="";b<c;b++)d+=a[b].value;return d}function q(a,b,c){var d=b.dir,e=b.next,g=e||d,h=c&&"parentNode"===g,i=M++;return b.first?function(b,c,e){for(;b=b[d];)if(1===b.nodeType||h)return a(b,c,e);return!1}:function(b,c,j){var k,l,m=[L,i];if(j){for(;b=b[d];)if((1===b.nodeType||h)&&a(b,c,j))return!0}else for(;b=b[d];)if(1===b.nodeType||h)if(l=b[K]||(b[K]={}),e&&f(b,e))b=b[d]||b;else{if((k=l[g])&&k[0]===L&&k[1]===i)return m[2]=k[2];if(l[g]=m,m[2]=a(b,c,j))return!0}return!1}}function r(a){return a.length>1?function(b,c,d){for(var e=a.length;e--;)if(!a[e](b,c,d))return!1;return!0}:a[0]}function s(a,b,d){for(var e=0,f=b.length;e<f;e++)c(a,b[e],d);return d}function t(a,b,c,d,e){for(var f,g=[],h=0,i=a.length,j=null!=b;h<i;h++)(f=a[h])&&(c&&!c(f,d,e)||(g.push(f),j&&b.push(h)));return g}function u(a,b,c,d,f,g){return d&&!d[K]&&(d=u(d)),f&&!f[K]&&(f=u(f,g)),e(function(e,g,h,i){var j,k,l,m,n=[],o=[],p=g.length,q=e||s(b||"*",h.nodeType?[h]:h,[]),r=!a||!e&&b?q:t(q,n,a,h,i);if(c?(m=f||(e?a:p||d)?[]:g,c(r,m,h,i)):m=r,d)for(j=t(m,o),d(j,[],h,i),k=j.length;k--;)(l=j[k])&&(m[o[k]]=!(r[o[k]]=l));if(e){if(f||a){if(f){for(j=[],k=m.length;k--;)(l=m[k])&&j.push(r[k]=l);f(null,m=[],j,i)}for(k=m.length;k--;)(l=m[k])&&(j=f?la.call(e,l):n[k])>-1&&(e[j]=!(g[j]=l))}}else m=t(m===g?m.splice(p,m.length):m),f?f(null,g,m,i):J.apply(g,m)})}function v(a){for(var b,c,d,e=a.length,f=A.relative[a[0].type],g=f||A.relative[" "],h=f?1:0,i=q(function(a){return a===b},g,!0),j=q(function(a){return la.call(b,a)>-1},g,!0),k=[function(a,c,d){var e=!f&&(d||c!=B)||((b=c).nodeType?i(a,c,d):j(a,c,d));return b=null,e}];h<e;h++)if(c=A.relative[a[h].type])k=[q(r(k),c)];else{if(c=A.filter[a[h].type].apply(null,a[h].matches),c[K]){for(d=++h;d<e&&!A.relative[a[d].type];d++);return u(h>1&&r(k),h>1&&p(a.slice(0,h-1).concat({value:" "===a[h-2].type?"*":""})).replace(Da,"$1"),c,h<d&&v(a.slice(h,d)),d<e&&v(a=a.slice(d)),d<e&&p(a))}k.push(c)}return r(k)}function w(a,b){var c=b.length>0,d=a.length>0,f=function(e,f,g,h,i){var j,k,l,n=0,o="0",p=e&&[],q=[],r=B,s=e||d&&A.find.TAG("*",i),u=L+=null==r?1:Math.random()||.1,v=s.length;for(i&&(B=f==E||f||i);o!==v&&null!=(j=s[o]);o++){if(d&&j){for(k=0,f||j.ownerDocument==E||(m(j),g=!G);l=a[k++];)if(l(j,f||E,g)){J.call(h,j);break}i&&(L=u)}c&&((j=!l&&j)&&n--,e&&p.push(j))}if(n+=o,c&&o!==n){for(k=0;l=b[k++];)l(p,q,f,g);if(e){if(n>0)for(;o--;)p[o]||q[o]||(q[o]=za.call(h));q=t(q)}J.apply(h,q),i&&!e&&q.length>0&&n+b.length>1&&ya.uniqueSort(h)}return i&&(L=u,B=r),p};return c?e(f):f}function x(a,b){var c,d=[],e=[],f=P[a+" "];if(!f){for(b||(b=o(a)),c=b.length;c--;)f=v(b[c]),f[K]?d.push(f):e.push(f);f=P(a,w(e,d)),f.selector=a}return f}function y(a,b,c,d){var e,f,g,h,i,j="function"==typeof a&&a,k=!d&&o(a=j.selector||a);if(c=c||[],1===k.length){if(f=k[0]=k[0].slice(0),f.length>2&&"ID"===(g=f[0]).type&&9===b.nodeType&&G&&A.relative[f[1].type]){if(!(b=(A.find.ID(g.matches[0].replace(fa,ha),b)||[])[0]))return c;j&&(b=b.parentNode),a=a.slice(f.shift().value.length)}for(e=aa.needsContext.test(a)?0:f.length;e--&&(g=f[e],!A.relative[h=g.type]);)if((i=A.find[h])&&(d=i(g.matches[0].replace(fa,ha),ea.test(f[0].type)&&l(b.parentNode)||b))){if(f.splice(e,1),!(a=d.length&&p(f)))return J.apply(c,d),c;break}}return(j||x(a,k))(d,b,!G,c,!b||ea.test(a)&&l(b.parentNode)||b),c}var z,A,B,C,D,E,F,G,H,I,J=Ga,K=ya.expando,L=0,M=0,N=d(),O=d(),P=d(),Q=d(),R=function(a,b){return a===b&&(D=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",T="(?:\\\\[\\da-fA-F]{1,6}"+Ca+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",U="\\["+Ca+"*("+T+")(?:"+Ca+"*([*^$|!~]?=)"+Ca+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+T+"))|)"+Ca+"*\\]",V=":("+T+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+U+")*)|.*)\\)|)",W=new RegExp(Ca+"+","g"),X=new RegExp("^"+Ca+"*,"+Ca+"*"),Y=new RegExp("^"+Ca+"*([>+~]|"+Ca+")"+Ca+"*"),Z=new RegExp(Ca+"|>"),$=new RegExp(V),_=new RegExp("^"+T+"$"),aa={ID:new RegExp("^#("+T+")"),CLASS:new RegExp("^\\.("+T+")"),TAG:new RegExp("^("+T+"|[*])"),ATTR:new RegExp("^"+U),PSEUDO:new RegExp("^"+V),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+Ca+"*(even|odd|(([+-]|)(\\d*)n|)"+Ca+"*(?:([+-]|)"+Ca+"*(\\d+)|))"+Ca+"*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^"+Ca+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+Ca+"*((?:-\\d)?\\d*)"+Ca+"*\\)|)(?=[^-]|$)","i")},ba=/^(?:input|select|textarea|button)$/i,ca=/^h\d$/i,da=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ea=/[+~]/,fa=new RegExp("\\\\[\\da-fA-F]{1,6}"+Ca+"?|\\\\([^\\r\\n\\f])","g"),ha=function(a,b){var c="0x"+a.slice(1)-65536;return b||(c<0?String.fromCharCode(c+65536):String.fromCharCode(c>>10|55296,1023&c|56320))},ja=function(){m()},ka=q(function(a){return!0===a.disabled&&f(a,"fieldset")},{dir:"parentNode",next:"legend"});try{J.apply(ga=ia.call(Fa.childNodes),Fa.childNodes),ga[Fa.childNodes.length].nodeType}catch(a){J={apply:function(a,b){Ga.apply(a,ia.call(b))},call:function(a){Ga.apply(a,ia.call(arguments,1))}}}c.matches=function(a,b){return c(a,null,null,b)},c.matchesSelector=function(a,b){if(m(a),G&&!Q[b+" "]&&(!H||!H.test(b)))try{var d=I.call(a,b);if(d||ra.disconnectedMatch||a.document&&11!==a.document.nodeType)return d}catch(a){Q(b,!0)}return c(b,E,null,[a]).length>0},c.contains=function(a,b){return(a.ownerDocument||a)!=E&&m(a),ya.contains(a,b)},c.attr=function(a,b){(a.ownerDocument||a)!=E&&m(a);var c=A.attrHandle[b.toLowerCase()],d=c&&oa.call(A.attrHandle,b.toLowerCase())?c(a,b,!G):void 0;return void 0!==d?d:a.getAttribute(b)},c.error=function(a){throw new Error("Syntax error, unrecognized expression: "+a)},ya.uniqueSort=function(a){var b,c=[],d=0,e=0;if(D=!ra.sortStable,C=!ra.sortStable&&ia.call(a,0),Aa.call(a,R),D){for(;b=a[e++];)b===a[e]&&(d=c.push(e));for(;d--;)Ba.call(a,c[d],1)}return C=null,a},ya.fn.uniqueSort=function(){return this.pushStack(ya.uniqueSort(ia.apply(this)))},A=ya.expr={cacheLength:50,createPseudo:e,match:aa,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(a){return a[1]=a[1].replace(fa,ha),a[3]=(a[3]||a[4]||a[5]||"").replace(fa,ha),"~="===a[2]&&(a[3]=" "+a[3]+" "),a.slice(0,4)},CHILD:function(a){return a[1]=a[1].toLowerCase(),"nth"===a[1].slice(0,3)?(a[3]||c.error(a[0]),a[4]=+(a[4]?a[5]+(a[6]||1):2*("even"===a[3]||"odd"===a[3])),a[5]=+(a[7]+a[8]||"odd"===a[3])):a[3]&&c.error(a[0]),a},PSEUDO:function(a){var b,c=!a[6]&&a[2];return aa.CHILD.test(a[0])?null:(a[3]?a[2]=a[4]||a[5]||"":c&&$.test(c)&&(b=o(c,!0))&&(b=c.indexOf(")",c.length-b)-c.length)&&(a[0]=a[0].slice(0,b),a[2]=c.slice(0,b)),a.slice(0,3))}},filter:{TAG:function(a){var b=a.replace(fa,ha).toLowerCase();return"*"===a?function(){return!0}:function(a){return f(a,b)}},CLASS:function(a){var b=N[a+" "];return b||(b=new RegExp("(^|"+Ca+")"+a+"("+Ca+"|$)"))&&N(a,function(a){return b.test("string"==typeof a.className&&a.className||void 0!==a.getAttribute&&a.getAttribute("class")||"")})},ATTR:function(a,b,d){return function(e){var f=c.attr(e,a);return null==f?"!="===b:!b||(f+="","="===b?f===d:"!="===b?f!==d:"^="===b?d&&0===f.indexOf(d):"*="===b?d&&f.indexOf(d)>-1:"$="===b?d&&f.slice(-d.length)===d:"~="===b?(" "+f.replace(W," ")+" ").indexOf(d)>-1:"|="===b&&(f===d||f.slice(0,d.length+1)===d+"-"))}},CHILD:function(a,b,c,d,e){var g="nth"!==a.slice(0,3),h="last"!==a.slice(-4),i="of-type"===b;return 1===d&&0===e?function(a){return!!a.parentNode}:function(b,c,j){var k,l,m,n,o,p=g!==h?"nextSibling":"previousSibling",q=b.parentNode,r=i&&b.nodeName.toLowerCase(),s=!j&&!i,t=!1;if(q){if(g){for(;p;){for(m=b;m=m[p];)if(i?f(m,r):1===m.nodeType)return!1;o=p="only"===a&&!o&&"nextSibling"}return!0}if(o=[h?q.firstChild:q.lastChild],h&&s){for(l=q[K]||(q[K]={}),k=l[a]||[],n=k[0]===L&&k[1],t=n&&k[2],m=n&&q.childNodes[n];m=++n&&m&&m[p]||(t=n=0)||o.pop();)if(1===m.nodeType&&++t&&m===b){l[a]=[L,n,t];break}}else if(s&&(l=b[K]||(b[K]={}),k=l[a]||[],n=k[0]===L&&k[1],t=n),!1===t)for(;(m=++n&&m&&m[p]||(t=n=0)||o.pop())&&((i?!f(m,r):1!==m.nodeType)||!++t||(s&&(l=m[K]||(m[K]={}),l[a]=[L,t]),m!==b)););return(t-=e)===d||t%d==0&&t/d>=0}}},PSEUDO:function(a,b){var d,f=A.pseudos[a]||A.setFilters[a.toLowerCase()]||c.error("unsupported pseudo: "+a);return f[K]?f(b):f.length>1?(d=[a,a,"",b],A.setFilters.hasOwnProperty(a.toLowerCase())?e(function(a,c){for(var d,e=f(a,b),g=e.length;g--;)d=la.call(a,e[g]),a[d]=!(c[d]=e[g])}):function(a){return f(a,0,d)}):f}},pseudos:{not:e(function(a){var b=[],c=[],d=x(a.replace(Da,"$1"));return d[K]?e(function(a,b,c,e){for(var f,g=d(a,null,e,[]),h=a.length;h--;)(f=g[h])&&(a[h]=!(b[h]=f))}):function(a,e,f){return b[0]=a,d(b,null,f,c),b[0]=null,!c.pop()}}),has:e(function(a){return function(b){return c(a,b).length>0}}),contains:e(function(a){return a=a.replace(fa,ha),function(b){return(b.textContent||ya.text(b)).indexOf(a)>-1}}),lang:e(function(a){return _.test(a||"")||c.error("unsupported lang: "+a),a=a.replace(fa,ha).toLowerCase(),function(b){var c;do{if(c=G?b.lang:b.getAttribute("xml:lang")||b.getAttribute("lang"))return(c=c.toLowerCase())===a||0===c.indexOf(a+"-")}while((b=b.parentNode)&&1===b.nodeType);return!1}}),target:function(b){var c=a.location&&a.location.hash;return c&&c.slice(1)===b.id},root:function(a){return a===F},focus:function(a){return a===b()&&E.hasFocus()&&!!(a.type||a.href||~a.tabIndex)},enabled:j(!1),disabled:j(!0),checked:function(a){return f(a,"input")&&!!a.checked||f(a,"option")&&!!a.selected},selected:function(a){return a.parentNode&&a.parentNode.selectedIndex,!0===a.selected},empty:function(a){for(a=a.firstChild;a;a=a.nextSibling)if(a.nodeType<6)return!1;return!0},parent:function(a){return!A.pseudos.empty(a)},header:function(a){return ca.test(a.nodeName)},input:function(a){return ba.test(a.nodeName)},button:function(a){return f(a,"input")&&"button"===a.type||f(a,"button")},text:function(a){var b;return f(a,"input")&&"text"===a.type&&(null==(b=a.getAttribute("type"))||"text"===b.toLowerCase())},first:k(function(){return[0]}),last:k(function(a,b){return[b-1]}),eq:k(function(a,b,c){return[c<0?c+b:c]}),even:k(function(a,b){for(var c=0;c<b;c+=2)a.push(c);return a}),odd:k(function(a,b){for(var c=1;c<b;c+=2)a.push(c);return a}),lt:k(function(a,b,c){var d;for(d=c<0?c+b:c>b?b:c;--d>=0;)a.push(d);return a}),gt:k(function(a,b,c){for(var d=c<0?c+b:c;++d<b;)a.push(d);return a})}},A.pseudos.nth=A.pseudos.eq;for(z in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})A.pseudos[z]=h(z);for(z in{submit:!0,reset:!0})A.pseudos[z]=i(z);n.prototype=A.filters=A.pseudos,A.setFilters=new n,ra.sortStable=K.split("").sort(R).join("")===K,m(),ra.sortDetached=g(function(a){return 1&a.compareDocumentPosition(E.createElement("fieldset"))}),ya.find=c,ya.expr[":"]=ya.expr.pseudos,ya.unique=ya.uniqueSort,c.compile=x,c.select=y,c.setDocument=m,c.tokenize=o,c.escape=ya.escapeSelector,c.getText=ya.text,c.isXML=ya.isXMLDoc,c.selectors=ya.expr,c.support=ya.support,c.uniqueSort=ya.uniqueSort}();var Ha=function(a,b,c){for(var d=[],e=void 0!==c;(a=a[b])&&9!==a.nodeType;)if(1===a.nodeType){if(e&&ya(a).is(c))break;d.push(a)}return d},Ia=function(a,b){for(var c=[];a;a=a.nextSibling)1===a.nodeType&&a!==b&&c.push(a);return c},Ja=ya.expr.match.needsContext,Ka=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;ya.filter=function(a,b,c){var d=b[0];return c&&(a=":not("+a+")"),1===b.length&&1===d.nodeType?ya.find.matchesSelector(d,a)?[d]:[]:ya.find.matches(a,ya.grep(b,function(a){return 1===a.nodeType}))},ya.fn.extend({find:function(a){var b,c,d=this.length,e=this;if("string"!=typeof a)return this.pushStack(ya(a).filter(function(){for(b=0;b<d;b++)if(ya.contains(e[b],this))return!0}));for(c=this.pushStack([]),b=0;b<d;b++)ya.find(a,e[b],c);return d>1?ya.uniqueSort(c):c},filter:function(a){return this.pushStack(h(this,a||[],!1))},not:function(a){return this.pushStack(h(this,a||[],!0))},is:function(a){return!!h(this,"string"==typeof a&&Ja.test(a)?ya(a):a||[],!1).length}});var La,Ma=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(ya.fn.init=function(a,b,c){var d,e;if(!a)return this;if(c=c||La,"string"==typeof a){if(!(d="<"===a[0]&&">"===a[a.length-1]&&a.length>=3?[null,a,null]:Ma.exec(a))||!d[1]&&b)return!b||b.jquery?(b||c).find(a):this.constructor(b).find(a);if(d[1]){if(b=b instanceof ya?b[0]:b,ya.merge(this,ya.parseHTML(d[1],b&&b.nodeType?b.ownerDocument||b:ua,!0)),Ka.test(d[1])&&ya.isPlainObject(b))for(d in b)sa(this[d])?this[d](b[d]):this.attr(d,b[d]);return this}return e=ua.getElementById(d[2]),e&&(this[0]=e,this.length=1),this}return a.nodeType?(this[0]=a,this.length=1,this):sa(a)?void 0!==c.ready?c.ready(a):a(ya):ya.makeArray(a,this)}).prototype=ya.fn,La=ya(ua);var Na=/^(?:parents|prev(?:Until|All))/,Oa={children:!0,contents:!0,next:!0,prev:!0};ya.fn.extend({has:function(a){var b=ya(a,this),c=b.length;return this.filter(function(){for(var a=0;a<c;a++)if(ya.contains(this,b[a]))return!0})},closest:function(a,b){var c,d=0,e=this.length,f=[],g="string"!=typeof a&&ya(a);if(!Ja.test(a))for(;d<e;d++)for(c=this[d];c&&c!==b;c=c.parentNode)if(c.nodeType<11&&(g?g.index(c)>-1:1===c.nodeType&&ya.find.matchesSelector(c,a))){f.push(c);break}return this.pushStack(f.length>1?ya.uniqueSort(f):f)},index:function(a){return a?"string"==typeof a?la.call(ya(a),this[0]):la.call(this,a.jquery?a[0]:a):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(a,b){return this.pushStack(ya.uniqueSort(ya.merge(this.get(),ya(a,b))))},addBack:function(a){return this.add(null==a?this.prevObject:this.prevObject.filter(a))}}),ya.each({parent:function(a){var b=a.parentNode;return b&&11!==b.nodeType?b:null},parents:function(a){return Ha(a,"parentNode")},parentsUntil:function(a,b,c){return Ha(a,"parentNode",c)},next:function(a){return i(a,"nextSibling")},prev:function(a){return i(a,"previousSibling")},nextAll:function(a){return Ha(a,"nextSibling")},prevAll:function(a){return Ha(a,"previousSibling")},nextUntil:function(a,b,c){return Ha(a,"nextSibling",c)},prevUntil:function(a,b,c){return Ha(a,"previousSibling",c)},siblings:function(a){return Ia((a.parentNode||{}).firstChild,a)},children:function(a){return Ia(a.firstChild)},contents:function(a){return null!=a.contentDocument&&ha(a.contentDocument)?a.contentDocument:(f(a,"template")&&(a=a.content||a),ya.merge([],a.childNodes))}},function(a,b){ya.fn[a]=function(c,d){var e=ya.map(this,b,c);return"Until"!==a.slice(-5)&&(d=c),d&&"string"==typeof d&&(e=ya.filter(d,e)),this.length>1&&(Oa[a]||ya.uniqueSort(e),Na.test(a)&&e.reverse()),this.pushStack(e)}});var Pa=/[^\x20\t\r\n\f]+/g;ya.Callbacks=function(a){a="string"==typeof a?j(a):ya.extend({},a);var b,c,e,f,g=[],h=[],i=-1,k=function(){for(f=f||a.once,e=b=!0;h.length;i=-1)for(c=h.shift();++i<g.length;)!1===g[i].apply(c[0],c[1])&&a.stopOnFalse&&(i=g.length,c=!1);a.memory||(c=!1),b=!1,f&&(g=c?[]:"")},l={add:function(){return g&&(c&&!b&&(i=g.length-1,h.push(c)),function b(c){ya.each(c,function(c,e){sa(e)?a.unique&&l.has(e)||g.push(e):e&&e.length&&"string"!==d(e)&&b(e)})}(arguments),c&&!b&&k()),this},remove:function(){return ya.each(arguments,function(a,b){for(var c;(c=ya.inArray(b,g,c))>-1;)g.splice(c,1),c<=i&&i--}),this},has:function(a){return a?ya.inArray(a,g)>-1:g.length>0},empty:function(){return g&&(g=[]),this},disable:function(){return f=h=[],g=c="",this},disabled:function(){return!g},lock:function(){return f=h=[],c||b||(g=c=""),this},locked:function(){return!!f},fireWith:function(a,c){return f||(c=c||[],c=[a,c.slice?c.slice():c],h.push(c),b||k()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!e}};return l},ya.extend({Deferred:function(b){var c=[["notify","progress",ya.Callbacks("memory"),ya.Callbacks("memory"),2],["resolve","done",ya.Callbacks("once memory"),ya.Callbacks("once memory"),0,"resolved"],["reject","fail",ya.Callbacks("once memory"),ya.Callbacks("once memory"),1,"rejected"]],d="pending",e={state:function(){return d},always:function(){return f.done(arguments).fail(arguments),this},catch:function(a){return e.then(null,a)},pipe:function(){var a=arguments;return ya.Deferred(function(b){ya.each(c,function(c,d){var e=sa(a[d[4]])&&a[d[4]];f[d[1]](function(){var a=e&&e.apply(this,arguments);a&&sa(a.promise)?a.promise().progress(b.notify).done(b.resolve).fail(b.reject):b[d[0]+"With"](this,e?[a]:arguments)})}),a=null}).promise()},then:function(b,d,e){function f(b,c,d,e){return function(){var h=this,i=arguments,j=function(){var a,j;if(!(b<g)){if((a=d.apply(h,i))===c.promise())throw new TypeError("Thenable self-resolution");j=a&&("object"==typeof a||"function"==typeof a)&&a.then,sa(j)?e?j.call(a,f(g,c,k,e),f(g,c,l,e)):(g++,j.call(a,f(g,c,k,e),f(g,c,l,e),f(g,c,k,c.notifyWith))):(d!==k&&(h=void 0,i=[a]),(e||c.resolveWith)(h,i))}},m=e?j:function(){try{j()}catch(a){ya.Deferred.exceptionHook&&ya.Deferred.exceptionHook(a,m.error),b+1>=g&&(d!==l&&(h=void 0,i=[a]),c.rejectWith(h,i))}};b?m():(ya.Deferred.getErrorHook?m.error=ya.Deferred.getErrorHook():ya.Deferred.getStackHook&&(m.error=ya.Deferred.getStackHook()),a.setTimeout(m))}}var g=0;return ya.Deferred(function(a){c[0][3].add(f(0,a,sa(e)?e:k,a.notifyWith)),c[1][3].add(f(0,a,sa(b)?b:k)),c[2][3].add(f(0,a,sa(d)?d:l))}).promise()},promise:function(a){return null!=a?ya.extend(a,e):e}},f={};return ya.each(c,function(a,b){var g=b[2],h=b[5];e[b[1]]=g.add,h&&g.add(function(){d=h},c[3-a][2].disable,c[3-a][3].disable,c[0][2].lock,c[0][3].lock),g.add(b[3].fire),f[b[0]]=function(){return f[b[0]+"With"](this===f?void 0:this,arguments),this},f[b[0]+"With"]=g.fireWith}),e.promise(f),b&&b.call(f,f),f},when:function(a){var b=arguments.length,c=b,d=Array(c),e=ia.call(arguments),f=ya.Deferred(),g=function(a){return function(c){d[a]=this,e[a]=arguments.length>1?ia.call(arguments):c,--b||f.resolveWith(d,e)}};if(b<=1&&(m(a,f.done(g(c)).resolve,f.reject,!b),"pending"===f.state()||sa(e[c]&&e[c].then)))return f.then();for(;c--;)m(e[c],g(c),f.reject);return f.promise()}});var Qa=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;ya.Deferred.exceptionHook=function(b,c){a.console&&a.console.warn&&b&&Qa.test(b.name)&&a.console.warn("jQuery.Deferred exception: "+b.message,b.stack,c)},ya.readyException=function(b){a.setTimeout(function(){throw b})};var Ra=ya.Deferred();ya.fn.ready=function(a){return Ra.then(a).catch(function(a){ya.readyException(a)}),this},ya.extend({isReady:!1,readyWait:1,ready:function(a){(!0===a?--ya.readyWait:ya.isReady)||(ya.isReady=!0,!0!==a&&--ya.readyWait>0||Ra.resolveWith(ua,[ya]))}}),ya.ready.then=Ra.then,"complete"===ua.readyState||"loading"!==ua.readyState&&!ua.documentElement.doScroll?a.setTimeout(ya.ready):(ua.addEventListener("DOMContentLoaded",n),a.addEventListener("load",n));var Sa=function(a,b,c,e,f,g,h){var i=0,j=a.length,k=null==c;if("object"===d(c)){f=!0;for(i in c)Sa(a,b,i,c[i],!0,g,h)}else if(void 0!==e&&(f=!0,sa(e)||(h=!0),k&&(h?(b.call(a,e),b=null):(k=b,b=function(a,b,c){return k.call(ya(a),c)})),b))for(;i<j;i++)b(a[i],c,h?e:e.call(a[i],i,b(a[i],c)));return f?a:k?b.call(a):j?b(a[0],c):g},Ta=/^-ms-/,Ua=/-([a-z])/g,Va=function(a){return 1===a.nodeType||9===a.nodeType||!+a.nodeType};q.uid=1,q.prototype={cache:function(a){var b=a[this.expando];return b||(b={},Va(a)&&(a.nodeType?a[this.expando]=b:Object.defineProperty(a,this.expando,{value:b,configurable:!0}))),b},set:function(a,b,c){var d,e=this.cache(a);if("string"==typeof b)e[p(b)]=c;else for(d in b)e[p(d)]=b[d];return e},get:function(a,b){return void 0===b?this.cache(a):a[this.expando]&&a[this.expando][p(b)]},access:function(a,b,c){return void 0===b||b&&"string"==typeof b&&void 0===c?this.get(a,b):(this.set(a,b,c),void 0!==c?c:b)},remove:function(a,b){var c,d=a[this.expando];if(void 0!==d){if(void 0!==b){Array.isArray(b)?b=b.map(p):(b=p(b),b=b in d?[b]:b.match(Pa)||[]),c=b.length;for(;c--;)delete d[b[c]]}(void 0===b||ya.isEmptyObject(d))&&(a.nodeType?a[this.expando]=void 0:delete a[this.expando])}},hasData:function(a){var b=a[this.expando];return void 0!==b&&!ya.isEmptyObject(b)}};var Wa=new q,Xa=new q,Ya=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Za=/[A-Z]/g;ya.extend({hasData:function(a){return Xa.hasData(a)||Wa.hasData(a)},data:function(a,b,c){return Xa.access(a,b,c)},removeData:function(a,b){Xa.remove(a,b)},_data:function(a,b,c){return Wa.access(a,b,c)},_removeData:function(a,b){Wa.remove(a,b)}}),ya.fn.extend({data:function(a,b){var c,d,e,f=this[0],g=f&&f.attributes;if(void 0===a){if(this.length&&(e=Xa.get(f),1===f.nodeType&&!Wa.get(f,"hasDataAttrs"))){for(c=g.length;c--;)g[c]&&(d=g[c].name,0===d.indexOf("data-")&&(d=p(d.slice(5)),s(f,d,e[d])));Wa.set(f,"hasDataAttrs",!0)}return e}return"object"==typeof a?this.each(function(){Xa.set(this,a)}):Sa(this,function(b){var c;if(f&&void 0===b){if(void 0!==(c=Xa.get(f,a)))return c;if(void 0!==(c=s(f,a)))return c}else this.each(function(){Xa.set(this,a,b)})},null,b,arguments.length>1,null,!0)},removeData:function(a){return this.each(function(){Xa.remove(this,a)})}}),ya.extend({queue:function(a,b,c){var d;if(a)return b=(b||"fx")+"queue",d=Wa.get(a,b),c&&(!d||Array.isArray(c)?d=Wa.access(a,b,ya.makeArray(c)):d.push(c)),d||[]},dequeue:function(a,b){b=b||"fx";var c=ya.queue(a,b),d=c.length,e=c.shift(),f=ya._queueHooks(a,b),g=function(){ya.dequeue(a,b)};"inprogress"===e&&(e=c.shift(),d--),e&&("fx"===b&&c.unshift("inprogress"),delete f.stop,e.call(a,g,f)),!d&&f&&f.empty.fire()},_queueHooks:function(a,b){var c=b+"queueHooks";return Wa.get(a,c)||Wa.access(a,c,{empty:ya.Callbacks("once memory").add(function(){Wa.remove(a,[b+"queue",c])})})}}),ya.fn.extend({queue:function(a,b){var c=2;return"string"!=typeof a&&(b=a,a="fx",c--),arguments.length<c?ya.queue(this[0],a):void 0===b?this:this.each(function(){var c=ya.queue(this,a,b);ya._queueHooks(this,a),"fx"===a&&"inprogress"!==c[0]&&ya.dequeue(this,a)})},dequeue:function(a){return this.each(function(){ya.dequeue(this,a)})},clearQueue:function(a){return this.queue(a||"fx",[])},promise:function(a,b){var c,d=1,e=ya.Deferred(),f=this,g=this.length,h=function(){--d||e.resolveWith(f,[f])};for("string"!=typeof a&&(b=a,a=void 0),a=a||"fx";g--;)(c=Wa.get(f[g],a+"queueHooks"))&&c.empty&&(d++,c.empty.add(h));return h(),e.promise(b)}});var $a=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,_a=new RegExp("^(?:([+-])=|)("+$a+")([a-z%]*)$","i"),ab=["Top","Right","Bottom","Left"],bb=ua.documentElement,cb=function(a){return ya.contains(a.ownerDocument,a)},db={composed:!0};bb.getRootNode&&(cb=function(a){return ya.contains(a.ownerDocument,a)||a.getRootNode(db)===a.ownerDocument});var eb=function(a,b){return a=b||a,"none"===a.style.display||""===a.style.display&&cb(a)&&"none"===ya.css(a,"display")},fb={};ya.fn.extend({show:function(){return v(this,!0)},hide:function(){return v(this)},toggle:function(a){return"boolean"==typeof a?a?this.show():this.hide():this.each(function(){eb(this)?ya(this).show():ya(this).hide()})}});var gb=/^(?:checkbox|radio)$/i,hb=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ib=/^$|^module$|\/(?:java|ecma)script/i;!function(){var a=ua.createDocumentFragment(),b=a.appendChild(ua.createElement("div")),c=ua.createElement("input");c.setAttribute("type","radio"),c.setAttribute("checked","checked"),c.setAttribute("name","t"),b.appendChild(c),ra.checkClone=b.cloneNode(!0).cloneNode(!0).lastChild.checked,b.innerHTML="<textarea>x</textarea>",ra.noCloneChecked=!!b.cloneNode(!0).lastChild.defaultValue,b.innerHTML="<option></option>",ra.option=!!b.lastChild}();var jb={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};jb.tbody=jb.tfoot=jb.colgroup=jb.caption=jb.thead,jb.th=jb.td,ra.option||(jb.optgroup=jb.option=[1,"<select multiple='multiple'>","</select>"]);var kb=/<|&#?\w+;/,lb=/^([^.]*)(?:\.(.+)|)/;ya.event={global:{},add:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q=Wa.get(a);if(Va(a))for(c.handler&&(f=c,c=f.handler,e=f.selector),e&&ya.find.matchesSelector(bb,e),c.guid||(c.guid=ya.guid++),(i=q.events)||(i=q.events=Object.create(null)),(g=q.handle)||(g=q.handle=function(b){return void 0!==ya&&ya.event.triggered!==b.type?ya.event.dispatch.apply(a,arguments):void 0}),b=(b||"").match(Pa)||[""],j=b.length;j--;)h=lb.exec(b[j])||[],n=p=h[1],o=(h[2]||"").split(".").sort(),n&&(l=ya.event.special[n]||{},n=(e?l.delegateType:l.bindType)||n,l=ya.event.special[n]||{},k=ya.extend({type:n,origType:p,data:d,handler:c,guid:c.guid,selector:e,needsContext:e&&ya.expr.match.needsContext.test(e),namespace:o.join(".")},f),(m=i[n])||(m=i[n]=[],m.delegateCount=0,l.setup&&!1!==l.setup.call(a,d,o,g)||a.addEventListener&&a.addEventListener(n,g)),l.add&&(l.add.call(a,k),k.handler.guid||(k.handler.guid=c.guid)),e?m.splice(m.delegateCount++,0,k):m.push(k),ya.event.global[n]=!0)},remove:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q=Wa.hasData(a)&&Wa.get(a);if(q&&(i=q.events)){for(b=(b||"").match(Pa)||[""],j=b.length;j--;)if(h=lb.exec(b[j])||[],n=p=h[1],o=(h[2]||"").split(".").sort(),n){for(l=ya.event.special[n]||{},n=(d?l.delegateType:l.bindType)||n,m=i[n]||[],h=h[2]&&new RegExp("(^|\\.)"+o.join("\\.(?:.*\\.|)")+"(\\.|$)"),g=f=m.length;f--;)k=m[f],!e&&p!==k.origType||c&&c.guid!==k.guid||h&&!h.test(k.namespace)||d&&d!==k.selector&&("**"!==d||!k.selector)||(m.splice(f,1),k.selector&&m.delegateCount--,l.remove&&l.remove.call(a,k));g&&!m.length&&(l.teardown&&!1!==l.teardown.call(a,o,q.handle)||ya.removeEvent(a,n,q.handle),delete i[n])}else for(n in i)ya.event.remove(a,n+b[j],c,d,!0);ya.isEmptyObject(i)&&Wa.remove(a,"handle events")}},dispatch:function(a){var b,c,d,e,f,g,h=new Array(arguments.length),i=ya.event.fix(a),j=(Wa.get(this,"events")||Object.create(null))[i.type]||[],k=ya.event.special[i.type]||{};for(h[0]=i,b=1;b<arguments.length;b++)h[b]=arguments[b];if(i.delegateTarget=this,!k.preDispatch||!1!==k.preDispatch.call(this,i)){for(g=ya.event.handlers.call(this,i,j),b=0;(e=g[b++])&&!i.isPropagationStopped();)for(i.currentTarget=e.elem,c=0;(f=e.handlers[c++])&&!i.isImmediatePropagationStopped();)i.rnamespace&&!1!==f.namespace&&!i.rnamespace.test(f.namespace)||(i.handleObj=f,i.data=f.data,void 0!==(d=((ya.event.special[f.origType]||{}).handle||f.handler).apply(e.elem,h))&&!1===(i.result=d)&&(i.preventDefault(),i.stopPropagation()));return k.postDispatch&&k.postDispatch.call(this,i),i.result}},handlers:function(a,b){var c,d,e,f,g,h=[],i=b.delegateCount,j=a.target;if(i&&j.nodeType&&!("click"===a.type&&a.button>=1))for(;j!==this;j=j.parentNode||this)if(1===j.nodeType&&("click"!==a.type||!0!==j.disabled)){for(f=[],g={},c=0;c<i;c++)d=b[c],e=d.selector+" ",void 0===g[e]&&(g[e]=d.needsContext?ya(e,this).index(j)>-1:ya.find(e,this,null,[j]).length),g[e]&&f.push(d);f.length&&h.push({elem:j,handlers:f})}return j=this,i<b.length&&h.push({elem:j,handlers:b.slice(i)}),h},addProp:function(a,b){Object.defineProperty(ya.Event.prototype,a,{enumerable:!0,configurable:!0,get:sa(b)?function(){if(this.originalEvent)return b(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[a]},set:function(b){Object.defineProperty(this,a,{enumerable:!0,configurable:!0,writable:!0,value:b})}})},fix:function(a){return a[ya.expando]?a:new ya.Event(a)},special:{load:{noBubble:!0},click:{setup:function(a){var b=this||a;return gb.test(b.type)&&b.click&&f(b,"input")&&C(b,"click",!0),!1},trigger:function(a){var b=this||a;return gb.test(b.type)&&b.click&&f(b,"input")&&C(b,"click"),!0},_default:function(a){var b=a.target;return gb.test(b.type)&&b.click&&f(b,"input")&&Wa.get(b,"click")||f(b,"a")}},beforeunload:{postDispatch:function(a){void 0!==a.result&&a.originalEvent&&(a.originalEvent.returnValue=a.result)}}}},ya.removeEvent=function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c)},ya.Event=function(a,b){if(!(this instanceof ya.Event))return new ya.Event(a,b);a&&a.type?(this.originalEvent=a,this.type=a.type,this.isDefaultPrevented=a.defaultPrevented||void 0===a.defaultPrevented&&!1===a.returnValue?z:A,this.target=a.target&&3===a.target.nodeType?a.target.parentNode:a.target,this.currentTarget=a.currentTarget,this.relatedTarget=a.relatedTarget):this.type=a,b&&ya.extend(this,b),this.timeStamp=a&&a.timeStamp||Date.now(),this[ya.expando]=!0},ya.Event.prototype={constructor:ya.Event,isDefaultPrevented:A,isPropagationStopped:A,isImmediatePropagationStopped:A,isSimulated:!1,preventDefault:function(){var a=this.originalEvent;this.isDefaultPrevented=z,a&&!this.isSimulated&&a.preventDefault()},stopPropagation:function(){var a=this.originalEvent;this.isPropagationStopped=z,a&&!this.isSimulated&&a.stopPropagation()},stopImmediatePropagation:function(){var a=this.originalEvent;this.isImmediatePropagationStopped=z,a&&!this.isSimulated&&a.stopImmediatePropagation(),this.stopPropagation()}},ya.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},ya.event.addProp),ya.each({focus:"focusin",blur:"focusout"},function(a,b){function c(a){if(ua.documentMode){var c=Wa.get(this,"handle"),d=ya.event.fix(a);d.type="focusin"===a.type?"focus":"blur",d.isSimulated=!0,c(a),d.target===d.currentTarget&&c(d)}else ya.event.simulate(b,a.target,ya.event.fix(a))}ya.event.special[a]={setup:function(){var d;if(C(this,a,!0),!ua.documentMode)return!1;d=Wa.get(this,b),d||this.addEventListener(b,c),Wa.set(this,b,(d||0)+1)},trigger:function(){return C(this,a),!0},teardown:function(){var a;if(!ua.documentMode)return!1;a=Wa.get(this,b)-1,a?Wa.set(this,b,a):(this.removeEventListener(b,c),Wa.remove(this,b))},_default:function(b){return Wa.get(b.target,a)},delegateType:b},ya.event.special[b]={setup:function(){var d=this.ownerDocument||this.document||this,e=ua.documentMode?this:d,f=Wa.get(e,b);f||(ua.documentMode?this.addEventListener(b,c):d.addEventListener(a,c,!0)),Wa.set(e,b,(f||0)+1)},teardown:function(){var d=this.ownerDocument||this.document||this,e=ua.documentMode?this:d,f=Wa.get(e,b)-1;f?Wa.set(e,b,f):(ua.documentMode?this.removeEventListener(b,c):d.removeEventListener(a,c,!0),Wa.remove(e,b))}}}),ya.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(a,b){ya.event.special[a]={delegateType:b,bindType:b,handle:function(a){var c,d=this,e=a.relatedTarget,f=a.handleObj;return e&&(e===d||ya.contains(d,e))||(a.type=f.origType,c=f.handler.apply(this,arguments),a.type=b),c}}}),ya.fn.extend({on:function(a,b,c,d){return B(this,a,b,c,d)},one:function(a,b,c,d){return B(this,a,b,c,d,1)},off:function(a,b,c){var d,e;if(a&&a.preventDefault&&a.handleObj)return d=a.handleObj,ya(a.delegateTarget).off(d.namespace?d.origType+"."+d.namespace:d.origType,d.selector,d.handler),this;if("object"==typeof a){for(e in a)this.off(e,b,a[e]);return this}return!1!==b&&"function"!=typeof b||(c=b,b=void 0),!1===c&&(c=A),this.each(function(){ya.event.remove(this,a,c,b)})}});var mb=/<script|<style|<link/i,nb=/checked\s*(?:[^=]|=\s*.checked.)/i,ob=/^\s*<!\[CDATA\[|\]\]>\s*$/g;ya.extend({htmlPrefilter:function(a){return a},clone:function(a,b,c){var d,e,f,g,h=a.cloneNode(!0),i=cb(a);if(!(ra.noCloneChecked||1!==a.nodeType&&11!==a.nodeType||ya.isXMLDoc(a)))for(g=w(h),f=w(a),d=0,e=f.length;d<e;d++)H(f[d],g[d]);if(b)if(c)for(f=f||w(a),g=g||w(h),d=0,e=f.length;d<e;d++)G(f[d],g[d]);else G(a,h);return g=w(h,"script"),g.length>0&&x(g,!i&&w(a,"script")),h},cleanData:function(a){for(var b,c,d,e=ya.event.special,f=0;void 0!==(c=a[f]);f++)if(Va(c)){if(b=c[Wa.expando]){if(b.events)for(d in b.events)e[d]?ya.event.remove(c,d):ya.removeEvent(c,d,b.handle);c[Wa.expando]=void 0}c[Xa.expando]&&(c[Xa.expando]=void 0)}}}),ya.fn.extend({detach:function(a){return J(this,a,!0)},remove:function(a){return J(this,a)},text:function(a){return Sa(this,function(a){return void 0===a?ya.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=a)})},null,a,arguments.length)},append:function(){return I(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){D(this,a).appendChild(a)}})},prepend:function(){return I(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=D(this,a);b.insertBefore(a,b.firstChild)}})},before:function(){return I(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this)})},after:function(){return I(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this.nextSibling)})},empty:function(){for(var a,b=0;null!=(a=this[b]);b++)1===a.nodeType&&(ya.cleanData(w(a,!1)),a.textContent="");return this},clone:function(a,b){return a=null!=a&&a,b=null==b?a:b,this.map(function(){return ya.clone(this,a,b)})},html:function(a){return Sa(this,function(a){var b=this[0]||{},c=0,d=this.length;if(void 0===a&&1===b.nodeType)return b.innerHTML;if("string"==typeof a&&!mb.test(a)&&!jb[(hb.exec(a)||["",""])[1].toLowerCase()]){a=ya.htmlPrefilter(a);try{for(;c<d;c++)b=this[c]||{},1===b.nodeType&&(ya.cleanData(w(b,!1)),b.innerHTML=a);b=0}catch(a){}}b&&this.empty().append(a)},null,a,arguments.length)},replaceWith:function(){var a=[];return I(this,arguments,function(b){var c=this.parentNode;ya.inArray(this,a)<0&&(ya.cleanData(w(this)),c&&c.replaceChild(b,this))},a)}}),ya.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(a,b){ya.fn[a]=function(a){for(var c,d=[],e=ya(a),f=e.length-1,g=0;g<=f;g++)c=g===f?this:this.clone(!0),ya(e[g])[b](c),ka.apply(d,c.get());return this.pushStack(d)}});var pb=new RegExp("^("+$a+")(?!px)[a-z%]+$","i"),qb=/^--/,rb=function(b){var c=b.ownerDocument.defaultView;return c&&c.opener||(c=a),c.getComputedStyle(b)},sb=function(a,b,c){var d,e,f={};for(e in b)f[e]=a.style[e],a.style[e]=b[e];d=c.call(a);for(e in b)a.style[e]=f[e];return d},tb=new RegExp(ab.join("|"),"i");!function(){function b(){if(k){j.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",k.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",bb.appendChild(j).appendChild(k);var b=a.getComputedStyle(k);d="1%"!==b.top,i=12===c(b.marginLeft),k.style.right="60%",g=36===c(b.right),e=36===c(b.width),k.style.position="absolute",f=12===c(k.offsetWidth/3),bb.removeChild(j),k=null}}function c(a){return Math.round(parseFloat(a))}var d,e,f,g,h,i,j=ua.createElement("div"),k=ua.createElement("div");k.style&&(k.style.backgroundClip="content-box",k.cloneNode(!0).style.backgroundClip="",ra.clearCloneStyle="content-box"===k.style.backgroundClip,ya.extend(ra,{boxSizingReliable:function(){return b(),e},pixelBoxStyles:function(){return b(),g},pixelPosition:function(){return b(),d},reliableMarginLeft:function(){return b(),i},scrollboxSize:function(){return b(),f},reliableTrDimensions:function(){var b,c,d,e;return null==h&&(b=ua.createElement("table"),c=ua.createElement("tr"),d=ua.createElement("div"),b.style.cssText="position:absolute;left:-11111px;border-collapse:separate",c.style.cssText="box-sizing:content-box;border:1px solid",c.style.height="1px",d.style.height="9px",d.style.display="block",bb.appendChild(b).appendChild(c).appendChild(d),e=a.getComputedStyle(c),h=parseInt(e.height,10)+parseInt(e.borderTopWidth,10)+parseInt(e.borderBottomWidth,10)===c.offsetHeight,bb.removeChild(b)),h}}))}();var ub=["Webkit","Moz","ms"],vb=ua.createElement("div").style,wb={},xb=/^(none|table(?!-c[ea]).+)/,yb={position:"absolute",visibility:"hidden",display:"block"},zb={letterSpacing:"0",fontWeight:"400"};ya.extend({cssHooks:{opacity:{get:function(a,b){if(b){var c=K(a,"opacity");return""===c?"1":c}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(a,b,c,d){if(a&&3!==a.nodeType&&8!==a.nodeType&&a.style){var e,f,g,h=p(b),i=qb.test(b),j=a.style;if(i||(b=N(h)),g=ya.cssHooks[b]||ya.cssHooks[h],void 0===c)return g&&"get"in g&&void 0!==(e=g.get(a,!1,d))?e:j[b];f=typeof c,"string"===f&&(e=_a.exec(c))&&e[1]&&(c=t(a,b,e),f="number"),null!=c&&c===c&&("number"!==f||i||(c+=e&&e[3]||(ya.cssNumber[h]?"":"px")),ra.clearCloneStyle||""!==c||0!==b.indexOf("background")||(j[b]="inherit"),g&&"set"in g&&void 0===(c=g.set(a,c,d))||(i?j.setProperty(b,c):j[b]=c))}},css:function(a,b,c,d){var e,f,g,h=p(b);return qb.test(b)||(b=N(h)),g=ya.cssHooks[b]||ya.cssHooks[h],g&&"get"in g&&(e=g.get(a,!0,c)),void 0===e&&(e=K(a,b,d)),"normal"===e&&b in zb&&(e=zb[b]),""===c||c?(f=parseFloat(e),!0===c||isFinite(f)?f||0:e):e}}),ya.each(["height","width"],function(a,b){ya.cssHooks[b]={get:function(a,c,d){if(c)return!xb.test(ya.css(a,"display"))||a.getClientRects().length&&a.getBoundingClientRect().width?Q(a,b,d):sb(a,yb,function(){return Q(a,b,d)})},set:function(a,c,d){var e,f=rb(a),g=!ra.scrollboxSize()&&"absolute"===f.position,h=g||d,i=h&&"border-box"===ya.css(a,"boxSizing",!1,f),j=d?P(a,b,d,i,f):0;return i&&g&&(j-=Math.ceil(a["offset"+b[0].toUpperCase()+b.slice(1)]-parseFloat(f[b])-P(a,b,"border",!1,f)-.5)),j&&(e=_a.exec(c))&&"px"!==(e[3]||"px")&&(a.style[b]=c,c=ya.css(a,b)),O(a,c,j)}}}),ya.cssHooks.marginLeft=L(ra.reliableMarginLeft,function(a,b){if(b)return(parseFloat(K(a,"marginLeft"))||a.getBoundingClientRect().left-sb(a,{marginLeft:0},function(){return a.getBoundingClientRect().left}))+"px"}),ya.each({margin:"",padding:"",border:"Width"},function(a,b){ya.cssHooks[a+b]={expand:function(c){for(var d=0,e={},f="string"==typeof c?c.split(" "):[c];d<4;d++)e[a+ab[d]+b]=f[d]||f[d-2]||f[0];return e}},"margin"!==a&&(ya.cssHooks[a+b].set=O)}),ya.fn.extend({css:function(a,b){return Sa(this,function(a,b,c){var d,e,f={},g=0;if(Array.isArray(b)){for(d=rb(a),e=b.length;g<e;g++)f[b[g]]=ya.css(a,b[g],!1,d);return f}return void 0!==c?ya.style(a,b,c):ya.css(a,b)},a,b,arguments.length>1)}}),ya.Tween=R,R.prototype={constructor:R,init:function(a,b,c,d,e,f){this.elem=a,this.prop=c,this.easing=e||ya.easing._default,this.options=b,this.start=this.now=this.cur(),this.end=d,this.unit=f||(ya.cssNumber[c]?"":"px")},cur:function(){var a=R.propHooks[this.prop];return a&&a.get?a.get(this):R.propHooks._default.get(this)},run:function(a){var b,c=R.propHooks[this.prop];return this.options.duration?this.pos=b=ya.easing[this.easing](a,this.options.duration*a,0,1,this.options.duration):this.pos=b=a,this.now=(this.end-this.start)*b+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),c&&c.set?c.set(this):R.propHooks._default.set(this),this}},R.prototype.init.prototype=R.prototype,R.propHooks={_default:{get:function(a){var b;return 1!==a.elem.nodeType||null!=a.elem[a.prop]&&null==a.elem.style[a.prop]?a.elem[a.prop]:(b=ya.css(a.elem,a.prop,""),b&&"auto"!==b?b:0)},set:function(a){ya.fx.step[a.prop]?ya.fx.step[a.prop](a):1!==a.elem.nodeType||!ya.cssHooks[a.prop]&&null==a.elem.style[N(a.prop)]?a.elem[a.prop]=a.now:ya.style(a.elem,a.prop,a.now+a.unit)}}},R.propHooks.scrollTop=R.propHooks.scrollLeft={set:function(a){a.elem.nodeType&&a.elem.parentNode&&(a.elem[a.prop]=a.now)}},ya.easing={linear:function(a){return a},swing:function(a){return.5-Math.cos(a*Math.PI)/2},_default:"swing"},ya.fx=R.prototype.init,ya.fx.step={};var Ab,Bb,Cb=/^(?:toggle|show|hide)$/,Db=/queueHooks$/;ya.Animation=ya.extend(Y,{tweeners:{"*":[function(a,b){var c=this.createTween(a,b);return t(c.elem,a,_a.exec(b),c),c}]},tweener:function(a,b){sa(a)?(b=a,a=["*"]):a=a.match(Pa);for(var c,d=0,e=a.length;d<e;d++)c=a[d],Y.tweeners[c]=Y.tweeners[c]||[],Y.tweeners[c].unshift(b)},prefilters:[W],prefilter:function(a,b){b?Y.prefilters.unshift(a):Y.prefilters.push(a)}}),ya.speed=function(a,b,c){var d=a&&"object"==typeof a?ya.extend({},a):{complete:c||!c&&b||sa(a)&&a,duration:a,easing:c&&b||b&&!sa(b)&&b};return ya.fx.off?d.duration=0:"number"!=typeof d.duration&&(d.duration in ya.fx.speeds?d.duration=ya.fx.speeds[d.duration]:d.duration=ya.fx.speeds._default),null!=d.queue&&!0!==d.queue||(d.queue="fx"),d.old=d.complete,d.complete=function(){sa(d.old)&&d.old.call(this),d.queue&&ya.dequeue(this,d.queue)},d},ya.fn.extend({fadeTo:function(a,b,c,d){return this.filter(eb).css("opacity",0).show().end().animate({opacity:b},a,c,d)},animate:function(a,b,c,d){var e=ya.isEmptyObject(a),f=ya.speed(b,c,d),g=function(){var b=Y(this,ya.extend({},a),f);(e||Wa.get(this,"finish"))&&b.stop(!0)};return g.finish=g,e||!1===f.queue?this.each(g):this.queue(f.queue,g)},stop:function(a,b,c){var d=function(a){var b=a.stop;delete a.stop,b(c)};return"string"!=typeof a&&(c=b,b=a,a=void 0),b&&this.queue(a||"fx",[]),this.each(function(){var b=!0,e=null!=a&&a+"queueHooks",f=ya.timers,g=Wa.get(this);if(e)g[e]&&g[e].stop&&d(g[e]);else for(e in g)g[e]&&g[e].stop&&Db.test(e)&&d(g[e]);for(e=f.length;e--;)f[e].elem!==this||null!=a&&f[e].queue!==a||(f[e].anim.stop(c),b=!1,f.splice(e,1));!b&&c||ya.dequeue(this,a)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var b,c=Wa.get(this),d=c[a+"queue"],e=c[a+"queueHooks"],f=ya.timers,g=d?d.length:0;for(c.finish=!0,ya.queue(this,a,[]),e&&e.stop&&e.stop.call(this,!0),b=f.length;b--;)f[b].elem===this&&f[b].queue===a&&(f[b].anim.stop(!0),f.splice(b,1));for(b=0;b<g;b++)d[b]&&d[b].finish&&d[b].finish.call(this);delete c.finish})}}),ya.each(["toggle","show","hide"],function(a,b){var c=ya.fn[b];ya.fn[b]=function(a,d,e){return null==a||"boolean"==typeof a?c.apply(this,arguments):this.animate(U(b,!0),a,d,e)}}),ya.each({slideDown:U("show"),slideUp:U("hide"),slideToggle:U("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(a,b){ya.fn[a]=function(a,c,d){return this.animate(b,a,c,d)}}),ya.timers=[],ya.fx.tick=function(){var a,b=0,c=ya.timers;for(Ab=Date.now();b<c.length;b++)(a=c[b])()||c[b]!==a||c.splice(b--,1);c.length||ya.fx.stop(),Ab=void 0},ya.fx.timer=function(a){ya.timers.push(a),ya.fx.start()},ya.fx.interval=13,ya.fx.start=function(){Bb||(Bb=!0,S())},ya.fx.stop=function(){Bb=null},ya.fx.speeds={slow:600,fast:200,_default:400},ya.fn.delay=function(b,c){return b=ya.fx?ya.fx.speeds[b]||b:b,c=c||"fx",this.queue(c,function(c,d){var e=a.setTimeout(c,b);d.stop=function(){a.clearTimeout(e)}})},function(){var a=ua.createElement("input"),b=ua.createElement("select"),c=b.appendChild(ua.createElement("option"));a.type="checkbox",ra.checkOn=""!==a.value,ra.optSelected=c.selected,a=ua.createElement("input"),a.value="t",a.type="radio",ra.radioValue="t"===a.value}();var Eb,Fb=ya.expr.attrHandle;ya.fn.extend({attr:function(a,b){return Sa(this,ya.attr,a,b,arguments.length>1)},removeAttr:function(a){return this.each(function(){ya.removeAttr(this,a)})}}),ya.extend({attr:function(a,b,c){var d,e,f=a.nodeType;if(3!==f&&8!==f&&2!==f)return void 0===a.getAttribute?ya.prop(a,b,c):(1===f&&ya.isXMLDoc(a)||(e=ya.attrHooks[b.toLowerCase()]||(ya.expr.match.bool.test(b)?Eb:void 0)),void 0!==c?null===c?void ya.removeAttr(a,b):e&&"set"in e&&void 0!==(d=e.set(a,c,b))?d:(a.setAttribute(b,c+""),c):e&&"get"in e&&null!==(d=e.get(a,b))?d:(d=ya.find.attr(a,b),null==d?void 0:d))},attrHooks:{type:{set:function(a,b){if(!ra.radioValue&&"radio"===b&&f(a,"input")){var c=a.value;return a.setAttribute("type",b),c&&(a.value=c),b}}}},removeAttr:function(a,b){var c,d=0,e=b&&b.match(Pa);if(e&&1===a.nodeType)for(;c=e[d++];)a.removeAttribute(c)}}),Eb={set:function(a,b,c){return!1===b?ya.removeAttr(a,c):a.setAttribute(c,c),c}},ya.each(ya.expr.match.bool.source.match(/\w+/g),function(a,b){var c=Fb[b]||ya.find.attr;Fb[b]=function(a,b,d){var e,f,g=b.toLowerCase();return d||(f=Fb[g],Fb[g]=e,e=null!=c(a,b,d)?g:null,Fb[g]=f),e}});var Gb=/^(?:input|select|textarea|button)$/i,Hb=/^(?:a|area)$/i;ya.fn.extend({prop:function(a,b){return Sa(this,ya.prop,a,b,arguments.length>1)},removeProp:function(a){return this.each(function(){delete this[ya.propFix[a]||a]})}}),ya.extend({prop:function(a,b,c){var d,e,f=a.nodeType;if(3!==f&&8!==f&&2!==f)return 1===f&&ya.isXMLDoc(a)||(b=ya.propFix[b]||b,e=ya.propHooks[b]),void 0!==c?e&&"set"in e&&void 0!==(d=e.set(a,c,b))?d:a[b]=c:e&&"get"in e&&null!==(d=e.get(a,b))?d:a[b]},propHooks:{tabIndex:{get:function(a){var b=ya.find.attr(a,"tabindex");return b?parseInt(b,10):Gb.test(a.nodeName)||Hb.test(a.nodeName)&&a.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),ra.optSelected||(ya.propHooks.selected={get:function(a){var b=a.parentNode;return b&&b.parentNode&&b.parentNode.selectedIndex,null},set:function(a){var b=a.parentNode;b&&(b.selectedIndex,b.parentNode&&b.parentNode.selectedIndex)}}),ya.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ya.propFix[this.toLowerCase()]=this}),ya.fn.extend({addClass:function(a){var b,c,d,e,f,g;return sa(a)?this.each(function(b){ya(this).addClass(a.call(this,b,$(this)))}):(b=_(a),b.length?this.each(function(){if(d=$(this),c=1===this.nodeType&&" "+Z(d)+" "){for(f=0;f<b.length;f++)e=b[f],c.indexOf(" "+e+" ")<0&&(c+=e+" ");g=Z(c),d!==g&&this.setAttribute("class",g)}}):this)},removeClass:function(a){var b,c,d,e,f,g;return sa(a)?this.each(function(b){ya(this).removeClass(a.call(this,b,$(this)))}):arguments.length?(b=_(a),b.length?this.each(function(){if(d=$(this),c=1===this.nodeType&&" "+Z(d)+" "){for(f=0;f<b.length;f++)for(e=b[f];c.indexOf(" "+e+" ")>-1;)c=c.replace(" "+e+" "," ");g=Z(c),d!==g&&this.setAttribute("class",g)}}):this):this.attr("class","")},toggleClass:function(a,b){var c,d,e,f,g=typeof a,h="string"===g||Array.isArray(a);return sa(a)?this.each(function(c){ya(this).toggleClass(a.call(this,c,$(this),b),b)}):"boolean"==typeof b&&h?b?this.addClass(a):this.removeClass(a):(c=_(a),this.each(function(){if(h)for(f=ya(this),e=0;e<c.length;e++)d=c[e],f.hasClass(d)?f.removeClass(d):f.addClass(d);else void 0!==a&&"boolean"!==g||(d=$(this),d&&Wa.set(this,"__className__",d),this.setAttribute&&this.setAttribute("class",d||!1===a?"":Wa.get(this,"__className__")||""))}))},hasClass:function(a){var b,c,d=0;for(b=" "+a+" ";c=this[d++];)if(1===c.nodeType&&(" "+Z($(c))+" ").indexOf(b)>-1)return!0;return!1}});var Ib=/\r/g;ya.fn.extend({val:function(a){var b,c,d,e=this[0];{if(arguments.length)return d=sa(a),this.each(function(c){var e;1===this.nodeType&&(e=d?a.call(this,c,ya(this).val()):a,null==e?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=ya.map(e,function(a){return null==a?"":a+""})),(b=ya.valHooks[this.type]||ya.valHooks[this.nodeName.toLowerCase()])&&"set"in b&&void 0!==b.set(this,e,"value")||(this.value=e))});if(e)return(b=ya.valHooks[e.type]||ya.valHooks[e.nodeName.toLowerCase()])&&"get"in b&&void 0!==(c=b.get(e,"value"))?c:(c=e.value,"string"==typeof c?c.replace(Ib,""):null==c?"":c)}}}),ya.extend({valHooks:{option:{get:function(a){var b=ya.find.attr(a,"value");return null!=b?b:Z(ya.text(a))}},select:{get:function(a){var b,c,d,e=a.options,g=a.selectedIndex,h="select-one"===a.type,i=h?null:[],j=h?g+1:e.length;for(d=g<0?j:h?g:0;d<j;d++)if(c=e[d],(c.selected||d===g)&&!c.disabled&&(!c.parentNode.disabled||!f(c.parentNode,"optgroup"))){if(b=ya(c).val(),h)return b;i.push(b)}return i},set:function(a,b){for(var c,d,e=a.options,f=ya.makeArray(b),g=e.length;g--;)d=e[g],(d.selected=ya.inArray(ya.valHooks.option.get(d),f)>-1)&&(c=!0);return c||(a.selectedIndex=-1),f}}}}),ya.each(["radio","checkbox"],function(){ya.valHooks[this]={set:function(a,b){if(Array.isArray(b))return a.checked=ya.inArray(ya(a).val(),b)>-1}},ra.checkOn||(ya.valHooks[this].get=function(a){return null===a.getAttribute("value")?"on":a.value})});var Jb=a.location,Kb={guid:Date.now()},Lb=/\?/;ya.parseXML=function(b){var c,d;if(!b||"string"!=typeof b)return null;try{c=(new a.DOMParser).parseFromString(b,"text/xml")}catch(a){}return d=c&&c.getElementsByTagName("parsererror")[0],c&&!d||ya.error("Invalid XML: "+(d?ya.map(d.childNodes,function(a){return a.textContent}).join("\n"):b)),c};var Mb=/^(?:focusinfocus|focusoutblur)$/,Nb=function(a){a.stopPropagation()};ya.extend(ya.event,{trigger:function(b,c,d,e){var f,g,h,i,j,k,l,m,n=[d||ua],o=oa.call(b,"type")?b.type:b,p=oa.call(b,"namespace")?b.namespace.split("."):[];if(g=m=h=d=d||ua,3!==d.nodeType&&8!==d.nodeType&&!Mb.test(o+ya.event.triggered)&&(o.indexOf(".")>-1&&(p=o.split("."),o=p.shift(),p.sort()),j=o.indexOf(":")<0&&"on"+o,b=b[ya.expando]?b:new ya.Event(o,"object"==typeof b&&b),b.isTrigger=e?2:3,b.namespace=p.join("."),b.rnamespace=b.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,b.result=void 0,b.target||(b.target=d),c=null==c?[b]:ya.makeArray(c,[b]),l=ya.event.special[o]||{},e||!l.trigger||!1!==l.trigger.apply(d,c))){if(!e&&!l.noBubble&&!ta(d)){for(i=l.delegateType||o,Mb.test(i+o)||(g=g.parentNode);g;g=g.parentNode)n.push(g),h=g;h===(d.ownerDocument||ua)&&n.push(h.defaultView||h.parentWindow||a)}for(f=0;(g=n[f++])&&!b.isPropagationStopped();)m=g,b.type=f>1?i:l.bindType||o,k=(Wa.get(g,"events")||Object.create(null))[b.type]&&Wa.get(g,"handle"),k&&k.apply(g,c),(k=j&&g[j])&&k.apply&&Va(g)&&(b.result=k.apply(g,c),!1===b.result&&b.preventDefault());return b.type=o,e||b.isDefaultPrevented()||l._default&&!1!==l._default.apply(n.pop(),c)||!Va(d)||j&&sa(d[o])&&!ta(d)&&(h=d[j],h&&(d[j]=null),ya.event.triggered=o,b.isPropagationStopped()&&m.addEventListener(o,Nb),d[o](),b.isPropagationStopped()&&m.removeEventListener(o,Nb),ya.event.triggered=void 0,h&&(d[j]=h)),b.result}},simulate:function(a,b,c){var d=ya.extend(new ya.Event,c,{type:a,isSimulated:!0});ya.event.trigger(d,null,b)}}),ya.fn.extend({trigger:function(a,b){return this.each(function(){ya.event.trigger(a,b,this)})},triggerHandler:function(a,b){var c=this[0];if(c)return ya.event.trigger(a,b,c,!0)}});var Ob=/\[\]$/,Pb=/\r?\n/g,Qb=/^(?:submit|button|image|reset|file)$/i,Rb=/^(?:input|select|textarea|keygen)/i;ya.param=function(a,b){var c,d=[],e=function(a,b){var c=sa(b)?b():b;d[d.length]=encodeURIComponent(a)+"="+encodeURIComponent(null==c?"":c)};if(null==a)return"";if(Array.isArray(a)||a.jquery&&!ya.isPlainObject(a))ya.each(a,function(){e(this.name,this.value)});else for(c in a)aa(c,a[c],b,e);return d.join("&")},ya.fn.extend({serialize:function(){return ya.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var a=ya.prop(this,"elements");return a?ya.makeArray(a):this}).filter(function(){var a=this.type;return this.name&&!ya(this).is(":disabled")&&Rb.test(this.nodeName)&&!Qb.test(a)&&(this.checked||!gb.test(a))}).map(function(a,b){var c=ya(this).val();return null==c?null:Array.isArray(c)?ya.map(c,function(a){return{name:b.name,value:a.replace(Pb,"\r\n")}}):{name:b.name,value:c.replace(Pb,"\r\n")}}).get()}});var Sb=/%20/g,Tb=/#.*$/,Ub=/([?&])_=[^&]*/,Vb=/^(.*?):[ \t]*([^\r\n]*)$/gm,Wb=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Xb=/^(?:GET|HEAD)$/,Yb=/^\/\//,Zb={},$b={},_b="*/".concat("*"),ac=ua.createElement("a");ac.href=Jb.href,ya.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Jb.href,type:"GET",isLocal:Wb.test(Jb.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":_b,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":ya.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(a,b){return b?da(da(a,ya.ajaxSettings),b):da(ya.ajaxSettings,a)},ajaxPrefilter:ba(Zb),ajaxTransport:ba($b),ajax:function(b,c){function d(b,c,d,h){var j,m,n,u,v,w=c;k||(k=!0,i&&a.clearTimeout(i),e=void 0,g=h||"",x.readyState=b>0?4:0,j=b>=200&&b<300||304===b,d&&(u=ea(o,x,d)),!j&&ya.inArray("script",o.dataTypes)>-1&&ya.inArray("json",o.dataTypes)<0&&(o.converters["text script"]=function(){}),u=fa(o,u,x,j),j?(o.ifModified&&(v=x.getResponseHeader("Last-Modified"),v&&(ya.lastModified[f]=v),(v=x.getResponseHeader("etag"))&&(ya.etag[f]=v)),204===b||"HEAD"===o.type?w="nocontent":304===b?w="notmodified":(w=u.state,m=u.data,n=u.error,j=!n)):(n=w,!b&&w||(w="error",b<0&&(b=0))),x.status=b,x.statusText=(c||w)+"",j?r.resolveWith(p,[m,w,x]):r.rejectWith(p,[x,w,n]),x.statusCode(t),t=void 0,l&&q.trigger(j?"ajaxSuccess":"ajaxError",[x,o,j?m:n]),s.fireWith(p,[x,w]),l&&(q.trigger("ajaxComplete",[x,o]),--ya.active||ya.event.trigger("ajaxStop")))}"object"==typeof b&&(c=b,b=void 0),c=c||{};var e,f,g,h,i,j,k,l,m,n,o=ya.ajaxSetup({},c),p=o.context||o,q=o.context&&(p.nodeType||p.jquery)?ya(p):ya.event,r=ya.Deferred(),s=ya.Callbacks("once memory"),t=o.statusCode||{},u={},v={},w="canceled",x={readyState:0,getResponseHeader:function(a){var b;if(k){if(!h)for(h={};b=Vb.exec(g);)h[b[1].toLowerCase()+" "]=(h[b[1].toLowerCase()+" "]||[]).concat(b[2]);b=h[a.toLowerCase()+" "]}return null==b?null:b.join(", ")},getAllResponseHeaders:function(){return k?g:null},setRequestHeader:function(a,b){return null==k&&(a=v[a.toLowerCase()]=v[a.toLowerCase()]||a,u[a]=b),this},overrideMimeType:function(a){return null==k&&(o.mimeType=a),this},statusCode:function(a){var b;if(a)if(k)x.always(a[x.status]);else for(b in a)t[b]=[t[b],a[b]];return this},abort:function(a){var b=a||w;return e&&e.abort(b),d(0,b),this}};if(r.promise(x),o.url=((b||o.url||Jb.href)+"").replace(Yb,Jb.protocol+"//"),o.type=c.method||c.type||o.method||o.type,o.dataTypes=(o.dataType||"*").toLowerCase().match(Pa)||[""],null==o.crossDomain){j=ua.createElement("a");try{j.href=o.url,j.href=j.href,o.crossDomain=ac.protocol+"//"+ac.host!=j.protocol+"//"+j.host}catch(a){o.crossDomain=!0}}if(o.data&&o.processData&&"string"!=typeof o.data&&(o.data=ya.param(o.data,o.traditional)),ca(Zb,o,c,x),k)return x;l=ya.event&&o.global,l&&0==ya.active++&&ya.event.trigger("ajaxStart"),o.type=o.type.toUpperCase(),o.hasContent=!Xb.test(o.type),f=o.url.replace(Tb,""),o.hasContent?o.data&&o.processData&&0===(o.contentType||"").indexOf("application/x-www-form-urlencoded")&&(o.data=o.data.replace(Sb,"+")):(n=o.url.slice(f.length),o.data&&(o.processData||"string"==typeof o.data)&&(f+=(Lb.test(f)?"&":"?")+o.data,delete o.data),!1===o.cache&&(f=f.replace(Ub,"$1"),n=(Lb.test(f)?"&":"?")+"_="+Kb.guid+++n),o.url=f+n),o.ifModified&&(ya.lastModified[f]&&x.setRequestHeader("If-Modified-Since",ya.lastModified[f]),ya.etag[f]&&x.setRequestHeader("If-None-Match",ya.etag[f])),(o.data&&o.hasContent&&!1!==o.contentType||c.contentType)&&x.setRequestHeader("Content-Type",o.contentType),x.setRequestHeader("Accept",o.dataTypes[0]&&o.accepts[o.dataTypes[0]]?o.accepts[o.dataTypes[0]]+("*"!==o.dataTypes[0]?", "+_b+"; q=0.01":""):o.accepts["*"]);for(m in o.headers)x.setRequestHeader(m,o.headers[m]);if(o.beforeSend&&(!1===o.beforeSend.call(p,x,o)||k))return x.abort();if(w="abort",s.add(o.complete),x.done(o.success),x.fail(o.error),e=ca($b,o,c,x)){if(x.readyState=1,l&&q.trigger("ajaxSend",[x,o]),k)return x;o.async&&o.timeout>0&&(i=a.setTimeout(function(){x.abort("timeout")},o.timeout));try{k=!1,e.send(u,d)}catch(a){if(k)throw a;d(-1,a)}}else d(-1,"No Transport");return x},getJSON:function(a,b,c){return ya.get(a,b,c,"json")},getScript:function(a,b){return ya.get(a,void 0,b,"script")}}),ya.each(["get","post"],function(a,b){ya[b]=function(a,c,d,e){return sa(c)&&(e=e||d,d=c,c=void 0),ya.ajax(ya.extend({url:a,type:b,dataType:e,data:c,success:d},ya.isPlainObject(a)&&a))}}),ya.ajaxPrefilter(function(a){var b;for(b in a.headers)"content-type"===b.toLowerCase()&&(a.contentType=a.headers[b]||"")}),ya._evalUrl=function(a,b,c){return ya.ajax({url:a,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(a){ya.globalEval(a,b,c)}})},ya.fn.extend({wrapAll:function(a){var b;return this[0]&&(sa(a)&&(a=a.call(this[0])),b=ya(a,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&b.insertBefore(this[0]),b.map(function(){for(var a=this;a.firstElementChild;)a=a.firstElementChild;return a}).append(this)),this},wrapInner:function(a){return sa(a)?this.each(function(b){ya(this).wrapInner(a.call(this,b))}):this.each(function(){var b=ya(this),c=b.contents();c.length?c.wrapAll(a):b.append(a)})},wrap:function(a){var b=sa(a);return this.each(function(c){ya(this).wrapAll(b?a.call(this,c):a)})},unwrap:function(a){return this.parent(a).not("body").each(function(){ya(this).replaceWith(this.childNodes)}),this}}),ya.expr.pseudos.hidden=function(a){return!ya.expr.pseudos.visible(a)},ya.expr.pseudos.visible=function(a){return!!(a.offsetWidth||a.offsetHeight||a.getClientRects().length)},ya.ajaxSettings.xhr=function(){try{return new a.XMLHttpRequest}catch(a){}};var bc={0:200,1223:204},cc=ya.ajaxSettings.xhr();ra.cors=!!cc&&"withCredentials"in cc,ra.ajax=cc=!!cc,ya.ajaxTransport(function(b){var c,d;if(ra.cors||cc&&!b.crossDomain)return{send:function(e,f){var g,h=b.xhr();if(h.open(b.type,b.url,b.async,b.username,b.password),b.xhrFields)for(g in b.xhrFields)h[g]=b.xhrFields[g];b.mimeType&&h.overrideMimeType&&h.overrideMimeType(b.mimeType),b.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest");for(g in e)h.setRequestHeader(g,e[g]);c=function(a){return function(){c&&(c=d=h.onload=h.onerror=h.onabort=h.ontimeout=h.onreadystatechange=null,"abort"===a?h.abort():"error"===a?"number"!=typeof h.status?f(0,"error"):f(h.status,h.statusText):f(bc[h.status]||h.status,h.statusText,"text"!==(h.responseType||"text")||"string"!=typeof h.responseText?{binary:h.response}:{text:h.responseText},h.getAllResponseHeaders()))}},h.onload=c(),d=h.onerror=h.ontimeout=c("error"),void 0!==h.onabort?h.onabort=d:h.onreadystatechange=function(){4===h.readyState&&a.setTimeout(function(){c&&d()})},c=c("abort");try{h.send(b.hasContent&&b.data||null)}catch(a){if(c)throw a}},abort:function(){c&&c()}}}),ya.ajaxPrefilter(function(a){a.crossDomain&&(a.contents.script=!1)}),ya.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(a){return ya.globalEval(a),a}}}),ya.ajaxPrefilter("script",function(a){void 0===a.cache&&(a.cache=!1),a.crossDomain&&(a.type="GET")}),ya.ajaxTransport("script",function(a){if(a.crossDomain||a.scriptAttrs){var b,c;return{send:function(d,e){b=ya("<script>").attr(a.scriptAttrs||{}).prop({charset:a.scriptCharset,src:a.url}).on("load error",c=function(a){b.remove(),c=null,a&&e("error"===a.type?404:200,a.type)}),ua.head.appendChild(b[0])},abort:function(){c&&c()}}}});var dc=[],ec=/(=)\?(?=&|$)|\?\?/;ya.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var a=dc.pop()||ya.expando+"_"+Kb.guid++;return this[a]=!0,a}}),ya.ajaxPrefilter("json jsonp",function(b,c,d){var e,f,g,h=!1!==b.jsonp&&(ec.test(b.url)?"url":"string"==typeof b.data&&0===(b.contentType||"").indexOf("application/x-www-form-urlencoded")&&ec.test(b.data)&&"data");if(h||"jsonp"===b.dataTypes[0])return e=b.jsonpCallback=sa(b.jsonpCallback)?b.jsonpCallback():b.jsonpCallback,h?b[h]=b[h].replace(ec,"$1"+e):!1!==b.jsonp&&(b.url+=(Lb.test(b.url)?"&":"?")+b.jsonp+"="+e),b.converters["script json"]=function(){return g||ya.error(e+" was not called"),g[0]},b.dataTypes[0]="json",f=a[e],a[e]=function(){g=arguments},d.always(function(){void 0===f?ya(a).removeProp(e):a[e]=f,b[e]&&(b.jsonpCallback=c.jsonpCallback,dc.push(e)),g&&sa(f)&&f(g[0]),g=f=void 0}),"script"}),ra.createHTMLDocument=function(){var a=ua.implementation.createHTMLDocument("").body;return a.innerHTML="<form></form><form></form>",2===a.childNodes.length}(),ya.parseHTML=function(a,b,c){if("string"!=typeof a)return[];"boolean"==typeof b&&(c=b,b=!1);var d,e,f;return b||(ra.createHTMLDocument?(b=ua.implementation.createHTMLDocument(""),d=b.createElement("base"),d.href=ua.location.href,b.head.appendChild(d)):b=ua),e=Ka.exec(a),f=!c&&[],e?[b.createElement(e[1])]:(e=y([a],b,f),f&&f.length&&ya(f).remove(),ya.merge([],e.childNodes))},ya.fn.load=function(a,b,c){var d,e,f,g=this,h=a.indexOf(" ");return h>-1&&(d=Z(a.slice(h)),a=a.slice(0,h)),sa(b)?(c=b,b=void 0):b&&"object"==typeof b&&(e="POST"),g.length>0&&ya.ajax({url:a,type:e||"GET",dataType:"html",data:b}).done(function(a){f=arguments,g.html(d?ya("<div>").append(ya.parseHTML(a)).find(d):a)}).always(c&&function(a,b){g.each(function(){c.apply(this,f||[a.responseText,b,a])})}),this},ya.expr.pseudos.animated=function(a){return ya.grep(ya.timers,function(b){return a===b.elem}).length},ya.offset={setOffset:function(a,b,c){var d,e,f,g,h,i,j,k=ya.css(a,"position"),l=ya(a),m={};"static"===k&&(a.style.position="relative"),h=l.offset(),f=ya.css(a,"top"),i=ya.css(a,"left"),j=("absolute"===k||"fixed"===k)&&(f+i).indexOf("auto")>-1,j?(d=l.position(),g=d.top,e=d.left):(g=parseFloat(f)||0,e=parseFloat(i)||0),sa(b)&&(b=b.call(a,c,ya.extend({},h))),null!=b.top&&(m.top=b.top-h.top+g),null!=b.left&&(m.left=b.left-h.left+e),"using"in b?b.using.call(a,m):l.css(m)}},ya.fn.extend({offset:function(a){if(arguments.length)return void 0===a?this:this.each(function(b){ya.offset.setOffset(this,a,b)});var b,c,d=this[0];if(d)return d.getClientRects().length?(b=d.getBoundingClientRect(),c=d.ownerDocument.defaultView,{top:b.top+c.pageYOffset,left:b.left+c.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var a,b,c,d=this[0],e={top:0,left:0};if("fixed"===ya.css(d,"position"))b=d.getBoundingClientRect();else{for(b=this.offset(),c=d.ownerDocument,a=d.offsetParent||c.documentElement;a&&(a===c.body||a===c.documentElement)&&"static"===ya.css(a,"position");)a=a.parentNode;a&&a!==d&&1===a.nodeType&&(e=ya(a).offset(),e.top+=ya.css(a,"borderTopWidth",!0),e.left+=ya.css(a,"borderLeftWidth",!0))}return{top:b.top-e.top-ya.css(d,"marginTop",!0),left:b.left-e.left-ya.css(d,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var a=this.offsetParent;a&&"static"===ya.css(a,"position");)a=a.offsetParent;return a||bb})}}),ya.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(a,b){var c="pageYOffset"===b;ya.fn[a]=function(d){return Sa(this,function(a,d,e){var f;if(ta(a)?f=a:9===a.nodeType&&(f=a.defaultView),void 0===e)return f?f[b]:a[d];f?f.scrollTo(c?f.pageXOffset:e,c?e:f.pageYOffset):a[d]=e},a,d,arguments.length)}}),ya.each(["top","left"],function(a,b){ya.cssHooks[b]=L(ra.pixelPosition,function(a,c){if(c)return c=K(a,b),pb.test(c)?ya(a).position()[b]+"px":c})}),ya.each({Height:"height",Width:"width"},function(a,b){ya.each({padding:"inner"+a,content:b,"":"outer"+a},function(c,d){ya.fn[d]=function(e,f){var g=arguments.length&&(c||"boolean"!=typeof e),h=c||(!0===e||!0===f?"margin":"border");return Sa(this,function(b,c,e){var f;return ta(b)?0===d.indexOf("outer")?b["inner"+a]:b.document.documentElement["client"+a]:9===b.nodeType?(f=b.documentElement,Math.max(b.body["scroll"+a],f["scroll"+a],b.body["offset"+a],f["offset"+a],f["client"+a])):void 0===e?ya.css(b,c,h):ya.style(b,c,e,h)},b,g?e:void 0,g)}})}),ya.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(a,b){ya.fn[b]=function(a){return this.on(b,a)}}),ya.fn.extend({bind:function(a,b,c){return this.on(a,null,b,c)},unbind:function(a,b){return this.off(a,null,b)},delegate:function(a,b,c,d){return this.on(b,a,c,d)},undelegate:function(a,b,c){return 1===arguments.length?this.off(a,"**"):this.off(b,a||"**",c)},hover:function(a,b){return this.on("mouseenter",a).on("mouseleave",b||a)}}),ya.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(a,b){ya.fn[b]=function(a,c){return arguments.length>0?this.on(b,null,a,c):this.trigger(b)}});var fc=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;ya.proxy=function(a,b){var c,d,e;if("string"==typeof b&&(c=a[b],b=a,a=c),sa(a))return d=ia.call(arguments,2),e=function(){return a.apply(b||this,d.concat(ia.call(arguments)))},e.guid=a.guid=a.guid||ya.guid++,e},ya.holdReady=function(a){a?ya.readyWait++:ya.ready(!0)},ya.isArray=Array.isArray,ya.parseJSON=JSON.parse,ya.nodeName=f,ya.isFunction=sa,ya.isWindow=ta,ya.camelCase=p,ya.type=d,ya.now=Date.now,ya.isNumeric=function(a){var b=ya.type(a);return("number"===b||"string"===b)&&!isNaN(a-parseFloat(a))},ya.trim=function(a){return null==a?"":(a+"").replace(fc,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return ya});var gc=a.jQuery,hc=a.$;return ya.noConflict=function(b){return a.$===ya&&(a.$=hc),b&&a.jQuery===ya&&(a.jQuery=gc),ya},void 0===b&&(a.jQuery=a.$=ya),ya}),/*!
 * Lightbox v2.11.5
 * by Lokesh Dhakar
 *
 * More info:
 * http://lokeshdhakar.com/projects/lightbox2/
 *
 * Copyright Lokesh Dhakar
 * Released under the MIT license
 * https://github.com/lokesh/lightbox2/blob/master/LICENSE
 *
 * @preserve
 */
function(a,b){"function"==typeof define&&define.amd?define(["jquery"],b):"object"==typeof exports?module.exports=b(require("jquery")):a.lightbox=b(a.jQuery)}(this,function(a){function b(b){this.album=[],this.currentImageIndex=void 0,this.init(),this.options=a.extend({},this.constructor.defaults),this.option(b)}return b.defaults={albumLabel:"Image %1 of %2",alwaysShowNavOnTouchDevices:!1,fadeDuration:600,fitImagesInViewport:!0,imageFadeDuration:600,positionFromTop:50,resizeDuration:700,showImageNumberLabel:!0,wrapAround:!1,disableScrolling:!1,sanitizeTitle:!1},b.prototype.option=function(b){a.extend(this.options,b)},b.prototype.imageCountLabel=function(a,b){return this.options.albumLabel.replace(/%1/g,a).replace(/%2/g,b)},b.prototype.init=function(){var b=this;a(document).ready(function(){b.enable(),b.build()})},b.prototype.enable=function(){var b=this;a("body").on("click","a[rel^=lightbox], area[rel^=lightbox], a[data-lightbox], area[data-lightbox]",function(c){return b.start(a(c.currentTarget)),!1})},b.prototype.build=function(){if(!(a("#lightbox").length>0)){var b=this;a('<div id="lightboxOverlay" tabindex="-1" class="lightboxOverlay"></div><div id="lightbox" tabindex="-1" class="lightbox"><div class="lb-outerContainer"><div class="lb-container"><img class="lb-image" src="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==" alt=""/><div class="lb-nav"><a class="lb-prev" role="button" tabindex="0" aria-label="Previous image" href="" ></a><a class="lb-next" role="button" tabindex="0" aria-label="Next image" href="" ></a></div><div class="lb-loader"><a class="lb-cancel" role="button" tabindex="0"></a></div></div></div><div class="lb-dataContainer"><div class="lb-data"><div class="lb-details"><span class="lb-caption"></span><span class="lb-number"></span></div><div class="lb-closeContainer"><a class="lb-close" role="button" tabindex="0"></a></div></div></div></div>').appendTo(a("body")),this.$lightbox=a("#lightbox"),this.$overlay=a("#lightboxOverlay"),this.$outerContainer=this.$lightbox.find(".lb-outerContainer"),this.$container=this.$lightbox.find(".lb-container"),this.$image=this.$lightbox.find(".lb-image"),this.$nav=this.$lightbox.find(".lb-nav"),this.containerPadding={top:parseInt(this.$container.css("padding-top"),10),right:parseInt(this.$container.css("padding-right"),10),bottom:parseInt(this.$container.css("padding-bottom"),10),left:parseInt(this.$container.css("padding-left"),10)},this.imageBorderWidth={top:parseInt(this.$image.css("border-top-width"),10),right:parseInt(this.$image.css("border-right-width"),10),bottom:parseInt(this.$image.css("border-bottom-width"),10),left:parseInt(this.$image.css("border-left-width"),10)},this.$overlay.hide().on("click",function(){return b.end(),!1}),this.$lightbox.hide().on("click",function(c){"lightbox"===a(c.target).attr("id")&&b.end()}),this.$outerContainer.on("click",function(c){return"lightbox"===a(c.target).attr("id")&&b.end(),!1}),this.$lightbox.find(".lb-prev").on("click",function(){return 0===b.currentImageIndex?b.changeImage(b.album.length-1):b.changeImage(b.currentImageIndex-1),!1}),this.$lightbox.find(".lb-next").on("click",function(){return b.currentImageIndex===b.album.length-1?b.changeImage(0):b.changeImage(b.currentImageIndex+1),!1}),this.$nav.on("mousedown",function(a){3===a.which&&(b.$nav.css("pointer-events","none"),b.$lightbox.one("contextmenu",function(){setTimeout(function(){this.$nav.css("pointer-events","auto")}.bind(b),0)}))}),this.$lightbox.find(".lb-loader, .lb-close").on("click keyup",function(a){if("click"===a.type||"keyup"===a.type&&(13===a.which||32===a.which))return b.end(),!1})}},b.prototype.start=function(b){function c(a){d.album.push({alt:a.attr("data-alt"),link:a.attr("href"),title:a.attr("data-title")||a.attr("title")})}var d=this,e=a(window);e.on("resize",a.proxy(this.sizeOverlay,this)),this.sizeOverlay(),this.album=[];var f,g=0,h=b.attr("data-lightbox");if(h){f=a(b.prop("tagName")+'[data-lightbox="'+h+'"]');for(var i=0;i<f.length;i=++i)c(a(f[i])),f[i]===b[0]&&(g=i)}else if("lightbox"===b.attr("rel"))c(b);else{f=a(b.prop("tagName")+'[rel="'+b.attr("rel")+'"]');for(var j=0;j<f.length;j=++j)c(a(f[j])),f[j]===b[0]&&(g=j)}var k=e.scrollTop()+this.options.positionFromTop,l=e.scrollLeft();this.$lightbox.css({top:k+"px",left:l+"px"}).fadeIn(this.options.fadeDuration),this.options.disableScrolling&&a("body").addClass("lb-disable-scrolling"),this.changeImage(g)},b.prototype.changeImage=function(b){var c=this,d=this.album[b].link,e=d.split(".").slice(-1)[0],f=this.$lightbox.find(".lb-image");this.disableKeyboardNav(),this.$overlay.fadeIn(this.options.fadeDuration),a(".lb-loader").fadeIn("slow"),this.$lightbox.find(".lb-image, .lb-nav, .lb-prev, .lb-next, .lb-dataContainer, .lb-numbers, .lb-caption").hide(),this.$outerContainer.addClass("animating");var g=new Image;g.onload=function(){var h,i,j,k,l,m;f.attr({alt:c.album[b].alt,src:d}),a(g),f.width(g.width),f.height(g.height);var n=g.width/g.height;m=a(window).width(),l=a(window).height(),k=m-c.containerPadding.left-c.containerPadding.right-c.imageBorderWidth.left-c.imageBorderWidth.right-20,j=l-c.containerPadding.top-c.containerPadding.bottom-c.imageBorderWidth.top-c.imageBorderWidth.bottom-c.options.positionFromTop-70,"svg"===e?(n>=1?(i=k,h=parseInt(k/n,10)):(i=parseInt(j/n,10),h=j),f.width(i),f.height(h)):(c.options.fitImagesInViewport?(c.options.maxWidth&&c.options.maxWidth<k&&(k=c.options.maxWidth),c.options.maxHeight&&c.options.maxHeight<j&&(j=c.options.maxHeight)):(k=c.options.maxWidth||g.width||k,j=c.options.maxHeight||g.height||j),(g.width>k||g.height>j)&&(g.width/k>g.height/j?(i=k,h=parseInt(g.height/(g.width/i),10),f.width(i),f.height(h)):(h=j,i=parseInt(g.width/(g.height/h),10),f.width(i),f.height(h)))),c.sizeContainer(f.width(),f.height())},g.src=this.album[b].link,this.currentImageIndex=b},b.prototype.sizeOverlay=function(){var b=this;setTimeout(function(){b.$overlay.width(a(document).width()).height(a(document).height())},0)},b.prototype.sizeContainer=function(a,b){function c(){d.$lightbox.find(".lb-dataContainer").width(g),d.$lightbox.find(".lb-prevLink").height(h),d.$lightbox.find(".lb-nextLink").height(h),d.$overlay.trigger("focus"),d.showImage()}var d=this,e=this.$outerContainer.outerWidth(),f=this.$outerContainer.outerHeight(),g=a+this.containerPadding.left+this.containerPadding.right+this.imageBorderWidth.left+this.imageBorderWidth.right,h=b+this.containerPadding.top+this.containerPadding.bottom+this.imageBorderWidth.top+this.imageBorderWidth.bottom;e!==g||f!==h?this.$outerContainer.animate({width:g,height:h},this.options.resizeDuration,"swing",function(){c()}):c()},b.prototype.showImage=function(){this.$lightbox.find(".lb-loader").stop(!0).hide(),this.$lightbox.find(".lb-image").fadeIn(this.options.imageFadeDuration),this.updateNav(),this.updateDetails(),this.preloadNeighboringImages(),this.enableKeyboardNav()},b.prototype.updateNav=function(){var a=!1;try{document.createEvent("TouchEvent"),a=!!this.options.alwaysShowNavOnTouchDevices}catch(a){}this.$lightbox.find(".lb-nav").show(),this.album.length>1&&(this.options.wrapAround?(a&&this.$lightbox.find(".lb-prev, .lb-next").css("opacity","1"),this.$lightbox.find(".lb-prev, .lb-next").show()):(this.currentImageIndex>0&&(this.$lightbox.find(".lb-prev").show(),a&&this.$lightbox.find(".lb-prev").css("opacity","1")),this.currentImageIndex<this.album.length-1&&(this.$lightbox.find(".lb-next").show(),a&&this.$lightbox.find(".lb-next").css("opacity","1"))))},b.prototype.updateDetails=function(){var a=this;if(void 0!==this.album[this.currentImageIndex].title&&""!==this.album[this.currentImageIndex].title){var b=this.$lightbox.find(".lb-caption");this.options.sanitizeTitle?b.text(this.album[this.currentImageIndex].title):b.html(this.album[this.currentImageIndex].title),b.fadeIn("fast")}if(this.album.length>1&&this.options.showImageNumberLabel){var c=this.imageCountLabel(this.currentImageIndex+1,this.album.length);this.$lightbox.find(".lb-number").text(c).fadeIn("fast")}else this.$lightbox.find(".lb-number").hide();this.$outerContainer.removeClass("animating"),this.$lightbox.find(".lb-dataContainer").fadeIn(this.options.resizeDuration,function(){return a.sizeOverlay()})},b.prototype.preloadNeighboringImages=function(){if(this.album.length>this.currentImageIndex+1){(new Image).src=this.album[this.currentImageIndex+1].link}if(this.currentImageIndex>0){(new Image).src=this.album[this.currentImageIndex-1].link}},b.prototype.enableKeyboardNav=function(){this.$lightbox.on("keyup.keyboard",a.proxy(this.keyboardAction,this)),this.$overlay.on("keyup.keyboard",a.proxy(this.keyboardAction,this))},b.prototype.disableKeyboardNav=function(){this.$lightbox.off(".keyboard"),this.$overlay.off(".keyboard")},b.prototype.keyboardAction=function(a){var b=a.keyCode;27===b?(a.stopPropagation(),this.end()):37===b?0!==this.currentImageIndex?this.changeImage(this.currentImageIndex-1):this.options.wrapAround&&this.album.length>1&&this.changeImage(this.album.length-1):39===b&&(this.currentImageIndex!==this.album.length-1?this.changeImage(this.currentImageIndex+1):this.options.wrapAround&&this.album.length>1&&this.changeImage(0))},b.prototype.end=function(){this.disableKeyboardNav(),a(window).off("resize",this.sizeOverlay),this.$lightbox.fadeOut(this.options.fadeDuration),this.$overlay.fadeOut(this.options.fadeDuration),this.options.disableScrolling&&a("body").removeClass("lb-disable-scrolling")},new b});
//# sourceMappingURL=lightbox-plus-jquery.min.map