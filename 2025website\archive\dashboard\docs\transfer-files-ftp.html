<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Configure FTP Access</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_transfer-files-ftp">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/transfer-files-ftp.pdf">          Download PDF
          <span>transfer-files-ftp.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Configure FTP Access</h1>
<div class="paragraph">
<p>XAMPP includes <a href="http://www.proftpd.org/">proFTPD</a>, an open-source FTP server. This makes it easy to transfer files to and from a XAMPP environment using FTP.</p>
</div>
<div class="paragraph">
<p>To illustrate, assume that you have a simple PHP script named <em>example.php</em> in your home directory containing the following code, that you wish to transfer to the XAMPP server.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>&lt;!-- example.php --&gt;
&lt;html&gt;
 &lt;head&gt;&lt;/head&gt;
 &lt;body&gt;
   &lt;h2&gt;&lt;?php echo "Hello. Today is " . date('l'); ?&gt;.&lt;/h2&gt;
 &lt;/body&gt;
&lt;/html&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>To transfer files via the proFTPD server, you first need to configure FTP access rules. Follow these steps.</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Open a new terminal and ensure you are logged in as an administrator.</p>
</li>
<li>
<p>Create a new group named <em>ftp</em>. This group will contain those user accounts allowed to upload files via FTP.</p>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image1.png" alt="image1">
</div>
</div>
</li>
<li>
<p>Add your account (in this example, <em>susan</em>) to the new group. Add other users if needed.</p>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image2.png" alt="image2">
</div>
</div>
</li>
<li>
<p>Change the ownership and permissions of the <em>htdocs/</em> subdirectory of the XAMPP installation directory (typically, <em>/Applications/XAMPP/</em>) so that it is writable by the the new <em>ftp</em> group.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>chown root:ftp htdocs</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>chmod 775 htdocs</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image3.png" alt="image3">
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you&#8217;re using XAMPP in a single-user scenario and there will only be one user transferring files via FTP, you can skip creating a new group and instead simply transfer full ownership of the <em>htdocs/</em> directory to that user. To do this, use a command like <em>chown susan:susan htdocs</em>.
</td>
</tr>
</table>
</div>
</li>
<li>
<p>Ensure that proFTPD is running in the XAMPP control panel.</p>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image4.png" alt="image4">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>You can now transfer files to the XAMPP server using the steps below:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Start an FTP client and enter connection details as below.</p>
<div class="ulist">
<ul>
<li>
<p>If you’re connecting to the server from the same system, use "127.0.0.1" as the host address. If you’re connecting from a different system, use the network hostname or IP address of the XAMPP server.</p>
</li>
<li>
<p>Use "21" as the port for a standard FTP connection or "22" for an SFTP connection.</p>
</li>
<li>
<p>Enter your system username and password as your FTP credentials.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Your FTP client should now connect to the server and enter the <em>/Applications/XAMPP/xamppfiles/htdocs/</em> directory, which is the default Web server document root.</p>
</div>
</li>
<li>
<p>Transfer the file from your home directory to the server using normal FTP transfer conventions. If you’re using a graphical FTP client, you can usually drag and drop the file from one directory to the other. If you’re using a command-line FTP client, you can use the FTP PUT command.</p>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image5.png" alt="image5">
</div>
</div>
</li>
<li>
<p>Once the file is successfully transferred, you should be able to see it in action by browsing to <a href="http://localhost/example.php" class="bare">http://localhost/example.php</a>, as shown below:</p>
<div class="imageblock">
<div class="content">
<img src="./images/transfer-files-ftp/image6.png" alt="image6">
</div>
</div>
</li>
</ol>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
