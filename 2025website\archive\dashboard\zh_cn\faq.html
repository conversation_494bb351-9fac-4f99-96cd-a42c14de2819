<!doctype html>
<html lang="zh_cn">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="zh_cn zh_cn_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/zh_cn/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/zh_cn/faq.html">常见问题</a></li>
              <li class="item "><a href="/dashboard/zh_cn/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>经常提到的问题</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>我如何在Mac OS X上安装XAMPP？</dt>
      <dd>
      <p>要安装XAMPP只需参考如下操作：</p>
      <ul>
        <li>打开 DMG-Image。</li>
        <li>双击图片，开始安装。</li></ul>
      <p>完成了。XAMPP现在已经在/Applications/XAMPP目录下安装完毕。</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>如何启动XAMPP？</dt>
      <dd>
      <p>要启动XAMPP只需打开XAMPP控制台并启动Apache、MySQL和ProFTPD。XAMPP控制台的名称是“manager-osx”。</p>      
      </dd>
      <dt>如何停止运行XAMPP？</dt>
      <dd>
      <p>要关闭XAMPP只需打开XAMPP控制台，然后关闭服务器。XAMPP控制台的名称是“manager-osx”。                                </p>      
      </dd>
      <dt>我如何检查是否一切正常？</dt>
      <dd>
      <p>在浏览器中输入以下链接：</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>XAMPP准备好了吗？</dt>
      <dd><p>XAMPP是一个非常优秀而又实用的开发测试环境，XAMPP的配置是开放的，允许开发者任意去修改或配置适合您的开发环境。XAMPP并非商业运行环境，用于商业运作将是一个致命的环境！</p>
      <p>如下是XAMPP不安全的配置：</p>
      <ol>
        <li>MySQL数据库管理帐号 (root)没设置密码。</li>
        <li>MySQL数据库可以通过网络打开。</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
      </ol>
      <p>解决简单问题的安全命令：</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>这会启动一个基本的安全检查，以便于确保XAMPP安装的安全。</p></dd>

      <dt>看到有XAMPP启动时的错误信息?</dt>
      <dd>
        <p>你可能会得到一些XAMPP启动错误信息</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>由于另外一个Apache正在运行，所以没能成功启动XAMPP-Apache。如想正常启动XAMPP-Apache，先把另外一个Apache停止运行。</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>这又是相同的一个原因，另外一个MYSQL正在运行，所以没能成功启动MYSQL。如想正常启动，先把另外一个MYSQL停止运行。</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>这个错误可能存在多种原因，Apache提示有多种错误的话，我们找出的错误：</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>如果收到任何错误信息，请访问 %{社区} 寻求帮助。</p>
      </dd>

      <dt>如何让XAMPP更安全？</dt>
      <dd>
        <p>默认安装时，XAMPP没有设置密码，但是我们不推荐这种可以被其他人访问的配置来运行XAMPP</p>
        <p>只需（以root用户）输入以下命令就可以进行简单的安全检察：</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>现在您应该可以在屏幕上看到如下对话框：</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>（1）设置密码就会用这个密码来保护XAMPP演示的页面（http://localhost/xampp/）。用户名为‘lampp’！</p>
        <p>通过调用这条命令您的XAMPP安装过程会更加安全。</p>
      </dd>

      <dt>如何为PHP激活OCI8/Oracle扩展？</dt>
      <dd>
        <p>启动OCI8/Oracle的PHP扩展，请执行以下命令：</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>下面的对话框就会启动：</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>现在扩展程序将会激活。</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
        <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
        <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
        <p>To enable remote access to phpMyAdmin, follow these steps:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>Within this file, find the lines below. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Then replace 'Require local' with 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Restart the Apache server using the XAMPP control panel.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>我如何备份/恢复我的XAMPP系统？</dt>
      <dd>
        <p><strong>警告：</strong> 备份和恢复功能目前还处于开发阶段，所以目前可能还不能工作。</p>
        <p>你可以通过调用如下命令来创建备份：</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>或</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>”secret“代表你的MySQL root密码。这行命令会产生以下输出：</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>文件 /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh（在上面的例子中）包含了你的备份的数据。请将它保存在安全的地方。</p>

        <p>在新机器上，你需要一个和你的最初/源机器上相同版本的AXMPP.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>你会看到一些类似这样的东西：</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>这样就可以了。记住那只是一个测试版本的功能。</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">版权所有(c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">博客</a></li>
            <li><a href="/privacy_policy.html">隐私政策</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN提供
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
