@keyframes caseStudyActive {
    0% {
        top: 150%;
    } 60% {
        top: 0;
    }
}

@keyframes caseStudyClose {
    0% {
        top: 0%;
    } 60% {
        top: 150%;
    }
}

.caseStudy {
    z-index: -1000;
    background: #FFF8EF;
    overflow-y: scroll;
    position: relative;
    position: fixed;
    opacity: 0;
    left: 0;
}

.caseStudy.active {
    width: 100%;
    height: 100vh;
    z-index: 1000;
    overflow-y: scroll;
    opacity: 1;
    animation: 1s caseStudyActive ease-in-out;
    top: 0;
    left: 0;
}

.caseStudy.close {
    width: 100%;
    height: 100vh;
    z-index: 1000;
    overflow-y: scroll;
    opacity: 1;
    animation: 1s caseStudyClose ease-in-out;
    top: 0;
    left: 0;
}

.caseStudy__close {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
}

.caseStudy__close[data-placement="top"] {
    justify-content: flex-end;
    position: absolute;
    top: 25px;
    right: 25px;
}

.caseStudy__close[data-placement="bottom"] {
    justify-content: center;
}

.caseStudy__hero--content {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 80px;
    row-gap: 20px;
}

.caseStudy__hero--col:nth-of-type(1) {
    flex: 1 0 58%;
}

.caseStudy__hero--col img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.caseStudy__hero--col:nth-of-type(2) {
    flex: 1 0 38%;
}

.caseStudy__swiper {
    height: 500px;
    width: 100%;
    overflow-x: hidden;
    position: relative;
}

.caseStudy__swiper::before,
.caseStudy__swiper::after {
  background: linear-gradient(to right, #FFF8EF 0%, rgba(255, 255, 255, 0) 100%);
  /* background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0) 100%); */
  content: "";
  height: 102%;
  position: absolute;
  width: 20%;
  z-index: 2;
}

.caseStudy__swiper::after {
  right: 0;
  top: 0;
  transform: rotateZ(180deg);
}
.caseStudy__swiper::before {
  left: 0;
  top: 0;
}

.caseStudy__swiper .swiper-pagination {
    position: absolute;
    bottom: 0;
    left: 0;

}

.caseStudy__swiper .swiper-slide {
    height: 500px;
    width: auto;
    min-width: 18%;
    background: #909DA3;
    color: #fff;
}

.caseStudy__swiper .swiper-slide img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.swiper-button-prev {
    background-image: url('../assets/svg/swiperPrev.svg') !important;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: center;
}

.swiper-button-next {
    background-image: url('../assets/svg/swiperNext.svg') !important;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: center;
    }

.swiper-button-next::after {
    display: none;
}

.caseStudy__swiper--pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
}

.swiper-button-prev::after {
    display: none;
}

.caseStudy__description {
    display: flex;
    justify-content: center;
    align-items: center;
}

.caseStudy__description p {
    margin: 0;
}

.caseStudy__description--wrapper {
    display: flex;
    flex-flow: row wrap;
    column-gap: 50px;
    row-gap: 25px;
}

.caseStudy__collaborators {
    display: flex;
    flex-flow: column nowrap;
    row-gap: 10px;
    /* flex: 1 1; */
    text-align: center;
}

.caseStudy__content {
    flex: 1 1 500px;
}

.caseStudy__collaborators p {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;
}

.caseStudy__testimonial {
    display: flex;
    flex-flow: column nowrap;
}

.caseStudy__testimonial--image {
    /* max-height: 850px; */
    overflow: hidden;
    height: 850px;
}

.caseStudy__testimonials--content h3 {
    text-transform: unset;
}

@media screen and (max-width: 1366px) {
    .caseStudy__hero--content {
        flex-flow: column nowrap;
    }

    .caseStudy__close[data-placement="top"] {
        top: 10px;
        right: 10px;
    }

    .caseStudy__swiper {
        height: 296px;
    }

    .caseStudy__swiper .swiper-slide {
        height: 296px;
        /* width: 372px; */
    }

    .caseStudy__testimonial--image {
        /* max-height: 850px; */
        overflow: hidden;
        height: 362px;
    }
}