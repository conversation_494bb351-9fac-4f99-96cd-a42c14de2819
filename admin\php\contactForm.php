<?php    
    header('Access-Control-Allow-Origin: *');

    use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
    use P<PERSON><PERSON>ailer\PHPMailer\Exception;
    use PHPMailer\PHPMailer\SMTP;

    require 'PHPMailer/Exception.php';
    require 'PHPMailer/PHPMailer.php';
    require 'PHPMailer/SMTP.php';

    //Create an instance; passing `true` enables exceptions
    $mail = new PHPMailer;

    //Server settings
    $mail->isSMTP();
    $mail->Host       = 'smtp.gmail.com';
    $mail->SMTPAuth   = true;
    $mail->Username   = '';
    $mail->Password   = '';
    $mail->SMTPSecure = 'ssl';
    $mail->Port       = 465;

    //Recipients
    $mail->setFrom('<EMAIL>', 'VW Website Admin');
    $mail->addAddress('<EMAIL>', 'Recipient');

    $firstName = filter_var($_POST["firstName"], FILTER_SANITIZE_STRING);
    $lastName = filter_var($_POST["lastName"], FILTER_SANITIZE_STRING);
    $email = filter_var($_POST["email"], FILTER_SANITIZE_STRING);
    $phoneNumber = filter_var($_POST["phoneNumber"], FILTER_SANITIZE_STRING);
    $message = filter_var($_POST["message"], FILTER_SANITIZE_STRING);

    $email_body = '
    <html>
        <head>
          <title>Contact Email</title>
        </head>
        
        <body>
          <p style="font-size: 18px; font-weight: bold;">You have received a new email inquiry from the website</p>
          <table width="100%" border-top="0.5" cellpadding="5" cellspacing="0" style="border-style: solid; border-width: 1px; border-color:#EAF2FA;">
            
          <table width="100%" border-top="0.5" cellpadding="5" cellspacing="0" style="border-style: solid; border-width: 1px; border-color:#EAF2FA;">
                          
            <tr bgcolor="#EAF2FA">
              <td colspan="2">
                  <font style="font-size:12px">
                      <strong>First Name:</strong>
                  </font>
               </td>
            </tr>
            <tr>
                <td width="20">&nbsp;</td>
                <td bgcolor="#FFFFFF">
                    '.$firstName.'
                </td>
            </tr>
            <tr bgcolor="#EAF2FA">
              <td colspan="2">
                  <font style="font-size:12px">
                      <strong>Last Name:</strong>
                  </font>
               </td>
            </tr>
            <tr>
                <td width="20">&nbsp;</td>
                <td bgcolor="#FFFFFF">
                    '.$lastName.'
                </td>
            </tr>
            <tr bgcolor="#EAF2FA">
              <td colspan="2">
                  <font style="font-size:12px">
                      <strong>Email:</strong>
                  </font>
               </td>
            </tr>
            <tr>
                <td width="20">&nbsp;</td>
                <td bgcolor="#FFFFFF">
                    '.$email.'
                </td>
            </tr>
            <tr bgcolor="#EAF2FA">
              <td colspan="2">
                  <font style="font-size:12px">
                      <strong>Phone Number:</strong>
                  </font>
               </td>
            </tr>
            <tr>
                <td width="20">&nbsp;</td>
                <td bgcolor="#FFFFFF">
                    '.$phoneNumber.'
                </td>
            </tr>
            <tr bgcolor="#EAF2FA">
              <td colspan="2">
                  <font style="font-size:12px">
                      <strong>Message:</strong>
                  </font>
               </td>
            </tr>
            <tr>
                <td width="20">&nbsp;</td>
                <td bgcolor="#FFFFFF">
                    '.$message.'
                </td>
            </tr>

          </table>
        </body>
        </html>
    ';

    //Content
    $mail->isHTML(true);
    $mail->Subject = 'Contact form';
    $mail->Body = $email_body;

    if (!$mail->send()) {
        echo json_encode( "Message could not be sent. <NAME_EMAIL>");
    } else {
        echo json_encode('Message has been sent!');
    }
?>