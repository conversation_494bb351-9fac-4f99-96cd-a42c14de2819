
<div class="planning__container--nav" data-color="grey">
<img src="/assets/svg/menuClosedLight.svg" width="40px" alt="Open" class="expand expandA">
</div>

<!-- <section class="planning__nav">
</section> -->

<section id="expandMenu" class="expand__menu" data-color="grey">
        <div style="position: absolute;top: 0;left: 0;width: 100%;height: 100px;display: flex;justify-content: center;align-items: center;">
            <a href="#contact" class="btn contactBtn btnContactPopup">Contact Us</a>
        </div>

        <div class="expand expandMenu__close">
            <img src="/assets/svg/projectsMenuCollapse.svg" width="40px" alt="Close">
        </div>

        <div class="nav__item--category expandMenu__content">
            <a href="/projects/homes" class="nav__item navPlanning__item nav__sync" data-page="homes" data-direction="horizontial">Homes</a>    
            <a href="/projects/commercial" class="nav__item navPlanning__item nav__sync" data-page="commercial" data-direction="horizontial">Commercial</a>    
            <a href="/projects/heritage" class="nav__item navPlanning__item nav__sync" data-page="heritage" data-direction="horizontial">Heritage</a>    
            <a href="/projects/community" class="nav__item navPlanning__item nav__sync" data-page="community" data-direction="horizontial">Community</a>    
            <a href="/projects/multiunit" class="nav__item navPlanning__item nav__sync" data-page="multiunit" data-direction="horizontial">Multi-Unit</a>    
            <a href="/projects/masterplanning" class="nav__item navPlanning__item nav__sync" data-page="masterplanning" data-direction="horizontial">Masterplanning</a>    
        </div>
</section>

<script>
    const expand = document.querySelectorAll('.expand');
    const expandMenu = document.querySelector('#expandMenu');

    expand.forEach(btn => btn.addEventListener('click', () => {
        expandMenu.classList.toggle('active');
    }))
</script>