// Portuguese (pt)
plupload.addI18n({"Stop Upload":"Parar envio","Upload URL might be wrong or doesn't exist.":"O URL para carregar os ficheiros pode estar errado ou não existir.","tb":"tb","Size":"<PERSON><PERSON><PERSON>","Close":"Fechar","You must specify either browse_button or drop_element.":"Deve especificar ou o botão de navegação browse_button ou o elemento de recolha drop_element.","Init error.":"Erro ao iniciar.","Add files to the upload queue and click the start button.":"Adicione ficheiros à fila e clique no botão iniciar.","List":"Lista","Filename":"Nome do ficheiro","%s specified, but cannot be found.":"%s definido mas não foi encontrado.","Image format either wrong or not supported.":"Formato da imagem errado ou não é suportado.","Status":"Estado","HTTP Error.":"Erro HTTP.","Start Upload":"Começar envio","Error: File too large:":"Erro: Ficheiro demasiado grande:","kb":"kb","Duplicate file error.":"Erro: ficheiro duplicado.","File size error.":"Tamanho do ficheiro errado.","N/A":"N/D","gb":"gb","Error: Invalid file extension:":"Erro: Extensão de ficheiro inválida:","Select files":"Seleccione ficheiros","%s already present in the queue.":"%s já se encontra em fila.","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Resolução excede os limites! <b>%s</b> suporta imagens até %wx%hpx.","File: %s":"Ficheiro: %s","b":"b","Uploaded %d/%d files":"Carregados %d/%d ficheiros","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Só é possível enviar %d ficheiro(s) de cada vez. Os restantes ficheiros foram excluídos.","%d files queued":"%d ficheiros em fila","File: %s, size: %d, max file size: %d":"Ficheiro: %s, tamanho: %d, tamanho máximo do ficheiro: %d","Thumbnails":"Miniaturas","Drag files here.":"Largar ficheiros aqui.","Runtime ran out of available memory.":"A execução esgotou a memória disponível.","File count error.":"Erro: contagem de ficheiros.","File extension error.":"Error de extensão do ficheiro.","mb":"mb","Add Files":"Adicionar ficheiros"});
