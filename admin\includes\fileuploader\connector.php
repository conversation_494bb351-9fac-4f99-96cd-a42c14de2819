<?php
/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2010 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This is the File Manager Connector for PHP.
 */

if ( !defined('K_COUCH_DIR') ) die(); // cannot be loaded directly

ob_start() ;

require( K_COUCH_DIR. 'includes/fileuploader/config.php' ) ;
require( K_COUCH_DIR. 'includes/fileuploader/util.php' ) ;
require( K_COUCH_DIR. 'includes/fileuploader/io.php' ) ;
require( K_COUCH_DIR. 'includes/fileuploader/basexml.php' ) ;
require( K_COUCH_DIR. 'includes/fileuploader/commands.php' ) ;
require( K_COUCH_DIR. 'includes/fileuploader/phpcompat.php') ;

if ( !$Config['Enabled'] )
	SendError( 1, 'This connector is disabled. Please check the "editor/filemanager/connectors/php/config.php" file' ) ;

if ( !defined('K_GALLERY_UPLOAD') ){
    DoResponse() ;
}

function DoResponse()
{
    if (!isset($_GET)) {
        global $_GET;
    }
	if ( !isset( $_GET['Command'] ) || !isset( $_GET['Type'] ) || !isset( $_GET['CurrentFolder'] ) )
		return ;

	// Get the main request informaiton.
	$sCommand		= $_GET['Command'] ;
	$sResourceType	= $_GET['Type'] ;
	$sCurrentFolder	= GetCurrentFolder() ;

	// Check if it is an allowed command
	if ( ! IsAllowedCommand( $sCommand ) )
		SendError( 1, 'The "' . $sCommand . '" command isn\'t allowed' ) ;

	// Check if it is an allowed type.
	if ( !IsAllowedType( $sResourceType ) )
		SendError( 1, 'Invalid type specified' ) ;

	// File Upload doesn't have to Return XML, so it must be intercepted before anything.
	if ( $sCommand == 'FileUpload' )
	{
		$res = FileUpload( $sResourceType, $sCurrentFolder, $sCommand ) ;
		if ( defined('K_GALLERY_UPLOAD') ){
		    global $_K_IMAGE;
		    $_K_IMAGE = $res;
		}
		return ;
	}

	CreateXmlHeader( $sCommand, $sResourceType, $sCurrentFolder ) ;

	// Execute the required command.
	switch ( $sCommand )
	{
		case 'GetFolders' :
			GetFolders( $sResourceType, $sCurrentFolder ) ;
			break ;
		case 'GetFoldersAndFiles' :
			GetFoldersAndFiles( $sResourceType, $sCurrentFolder ) ;
			break ;
		case 'CreateFolder' :
			CreateFolder( $sResourceType, $sCurrentFolder ) ;
			break ;
	}

	CreateXmlFooter() ;

	exit ;
}
?>
