/*! jquery.finger - v0.1.4 - 2015-12-02
* https://github.com/ngryman/jquery.finger
* Copyright (c) 2015 <PERSON>; Licensed MIT */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a("object"==typeof exports?require("jquery"):jQuery)}(function(a){function b(c){c.preventDefault(),a.event.remove(v,"click",b)}function c(a,b){return(q?b.originalEvent.touches[0]:b)["page"+a.toUpperCase()]}function d(c,d,e){var h=a.Event(d,x);a.event.trigger(h,{originalEvent:c},c.target),h.isDefaultPrevented()&&(~d.indexOf("tap")&&!q?a.event.add(v,"click",b):c.preventDefault()),e&&(a.event.remove(v,t+"."+u,f),a.event.remove(v,s+"."+u,g))}function e(e){var l=e.timeStamp||+new Date;j!=l&&(j=l,w.x=x.x=c("x",e),w.y=x.y=c("y",e),w.time=l,w.target=e.target,x.orientation=null,x.end=!1,h=!1,i=!1,k=setTimeout(function(){i=!0,d(e,"press")},y.pressDuration),a.event.add(v,t+"."+u,f),a.event.add(v,s+"."+u,g),y.preventDefault&&(e.preventDefault(),a.event.add(v,"click",b)))}function f(b){if(x.x=c("x",b),x.y=c("y",b),x.dx=x.x-w.x,x.dy=x.y-w.y,x.adx=Math.abs(x.dx),x.ady=Math.abs(x.dy),h=x.adx>y.motionThreshold||x.ady>y.motionThreshold){for(clearTimeout(k),x.orientation||(x.adx>x.ady?(x.orientation="horizontal",x.direction=x.dx>0?1:-1):(x.orientation="vertical",x.direction=x.dy>0?1:-1));b.target&&b.target!==w.target;)b.target=b.target.parentNode;return b.target!==w.target?(b.target=w.target,void g.call(this,a.Event(s+"."+u,b))):void d(b,"drag")}}function g(a){var b,c=a.timeStamp||+new Date,e=c-w.time;if(clearTimeout(k),h||i||a.target!==w.target)a.target=w.target,e<y.flickDuration&&d(a,"flick"),x.end=!0,b="drag";else{var f=l===a.target&&c-m<y.doubleTapInterval;b=f?"doubletap":"tap",l=f?null:w.target,m=c}d(a,b,!0)}var h,i,j,k,l,m,n=navigator.userAgent,o=/chrome/i.exec(n),p=/android/i.exec(n),q="ontouchstart"in window&&!(o&&!p),r=q?"touchstart":"mousedown",s=q?"touchend touchcancel":"mouseup mouseleave",t=q?"touchmove":"mousemove",u="finger",v=a("html")[0],w={},x={},y=a.Finger={pressDuration:300,doubleTapInterval:300,flickDuration:150,motionThreshold:5};return a.event.add(v,r+"."+u,e),a.each("tap doubletap press drag flick".split(" "),function(b,c){a.fn[c]=function(a){return a?this.bind(c,a):this.trigger(c)}}),y});
