@keyframes DropdownNav {
    from {
        transform: translateY(-200%);
    } to {
        transform: translateY(0);
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.parallax {  
    height: inherit;
    background-attachment: fixed !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

.swiper-button-next, .swiper-button-prev {
    color: #1A272C;
}

.swiper-pagination-bullet {
    background: #A15D3C;
}

.swiper-pagination-bullet-active {
    background: #A15D3C;
}

html, body {
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    height: 100%;
    font-style: normal;
    color: #2D5166;
    background: #FFF8EF;
    scroll-behavior: smooth;
    scroll-padding-top: 100px;
}

img {
    user-select: none;
}

.container {
    max-width: 2560px;
    margin: auto;
    overflow: hidden;
    position: relative;
}

button {
    cursor: pointer;
}

.cw-xl {
    max-width: 1640px;
    margin: auto;

    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.cw-lg {
    max-width: 1200px;

    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.cw-md {
    max-width: 1100px;

    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.cw-sm {
    max-width: 740px;

    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;
}

h1 {
    font-size: 65px;
    line-height: 70px;
    /* text-transform: uppercase; */
    letter-spacing: 0px;
    margin-bottom: 35px;
}

h2 {
    font-size: 50px;
    line-height: 60px;
    /* text-transform: uppercase; */
    margin-bottom: 25px;
}

h3 {
    font-size: 40px;
    line-height: 45px;
    /* text-transform: uppercase; */
    margin-bottom: 25px;
}

p {
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 30px;
}

a {
    color: inherit;
    text-decoration: none;
}

.gc {
    display: grid;
    grid-template-columns: 80px repeat(10, 1fr) 80px;
}

.sp {
    padding: 55px 80px;
    box-sizing: border-box;
}

.sp-b {
    padding-bottom: 55px;
    box-sizing: border-box;
}

.sp-t {
    padding-top: 55px;
    box-sizing: border-box;
}

.sp-y {
    padding-left: 80px;
    padding-right: 80px;
    box-sizing: border-box;
}

.sp-l {
    padding-left: 80px;
    box-sizing: border-box;
}

.sp-x {
    padding-top: 55px;
    padding-bottom: 55px;
    box-sizing: border-box;
}

.banner {
    background: #1A272C;
    color: #fff;
    width: 100%;

    box-shadow: 2.5px 2.5px 5px #1A272C50;

    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 25px;

    z-index: 10;
    position: relative;

    padding: 10px;

    height: 75px;
}

/* .banner__copy {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    text-align: center;
} */

.banner h3 {
    margin: -10px 0 0 0;
}

.banner__image {
    background: #fff;
    width: 75px;
    height: 75px;
}

.banner__image img {
    height: 100%;
    width: 100%;
    object-position: center;
    object-fit: cover;
}

.banner__emphasis {
    font-size: 150%;
    color: #A15D3C;
}

.nav__wrapper {
    height: 100px;
}

.nav {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
    column-gap: 30px;
    row-gap: 30px;
    padding: 25px;
    max-width: 2560px;
    margin: auto;
    overflow: hidden;
    width: 100%;
    height: 100px;
    z-index: 6;
    background: #fff8ef;
}

.nav.fixed {
    position: fixed;
    top: 0;
    /* left: 0; */
    /* animation: DropdownNav 0.5s ease-in-out; */
    box-shadow: 2.5px 2.5px 5px #1A272C50;

}

.nav__col {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
    column-gap: 30px;
    row-gap: 30px;
}

.nav__toggle {
    cursor: pointer;
}

.nav__logo {
    width: 50vw;
    max-width: 400px;
    opacity: 1;
    transition: opacity 0.25s ease-in-out;
}

.nav__logo.hide {
    opacity: 0;
    transition: opacity 0.25s ease-in-out;
    width: 0px;
    height: 0px;
}

.nav__menu, .expand__menu {
    box-sizing: border-box;

    position: fixed;
    top: 0;
    right: 0;
    width: 340px;
    height: 100vh;
    z-index: 10;
    padding: 25px;

    display: flex;
    flex-flow: column nowrap;
    justify-content: space-between;
    align-items: center;
    row-gap: 50px;

    min-height: min-content;
    overflow-y: scroll;
    transform: translateX(200%);

    transition: transform 0.25s ease-in-out;
}

.expand__menu {
    overflow-y: hidden;
    justify-content: center;
    width: 340px;

    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 25px;
}

.expandMenu__close {
    grid-column: 1 / 2;
    grid-row: 1 / 3;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.expandMenu__close img {
    width: 20px;
    height: auto;
}

.expandMenu__content {
    grid-column: 1 / 5;
    grid-row: 1 / 3;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;

}

.expand {
    height: calc(100% - 100px);
    width: 30px;
    object-position: center;
    object-fit: contain;
}

.expandA {
    position: fixed;
    top: 100px;
}

.nav__menu--row a svg g path,
.nav__menu--row a svg g rect {
    fill: #A15D3C;
    transition: all 0.25s ease-in-out;
}

.nav__menu--row a:hover svg g path,
.nav__menu--row a:hover svg g rect {
    fill: #915436;
    transition: all 0.25s ease-in-out;
}

.nav__menu.active, .expand__menu.active {
    transform: translateX(0);
    transition: transform 0.25s ease-in-out;
}

.nav__menu--row {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 25px;
    row-gap: 50px;
    width: 100%;
}

.nav__menu--row[data-type="top"] {
    flex-flow: row nowrap;
    justify-content: flex-end;
}

.nav__menu--row[data-type="links"] {
    flex-flow: row nowrap;
    justify-content: center;
}

.nav__item {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;

    font-size: 30px;
    line-height: 37px;
    letter-spacing: 3.5px;
    /* text-transform: uppercase; */
}

.nav__item--category {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    row-gap: 10px;
}

.nav__item--section {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;

    font-size: 30px;
    line-height: 37px;
    letter-spacing: 3.5px;
    /* text-transform: uppercase; */
}

.nav__item--sub {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;

    font-size: 23px;
    line-height: 28px;
}

.nav__item--sub[data-direction="horizontial"],
.nav__item[data-direction="horizontial"] {
    border-bottom: 3px solid transparent;
    transition: all 0.25s ease-in-out;
}

.nav__item--sub[data-direction="horizontial"]:hover,
.nav__item[data-direction="horizontial"]:hover,
.nav__item--sub[data-direction="horizontial"].active,
.nav__item[data-direction="horizontial"].active {
    border-bottom: 3px solid #A15D3C;
    transition: all 0.25s ease-in-out;
}

.nav__item[data-direction="vertical"],
.nav__item--sub[data-direction="vertical"] {
    color: #909DA3;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    font-size: 25px;
    line-height: 25px;
    letter-spacing: 3px;
    transition: all 0.25s ease-in-out;
}

.nav__item[data-direction="vertical"]:hover,
.nav__item--sub[data-direction="vertical"]:hover,
.nav__item[data-direction="vertical"].active,
.nav__item--sub[data-direction="vertical"].active {
    color: #FFF8EF;
    transition: all 0.25s ease-in-out;
}

.hero[data-phase="1"] {
    /* height: 100vh; */
    max-height: 1280px;
    grid-template-rows: repeat(12, 1fr);
    padding-top: 25px;
}

.hero[data-phase="2"] {
    display: flex;
    flex-flow: column nowrap;
    position: relative;
    grid-template-rows: repeat(12, 1fr);
}

.hero__video {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 1080px;
    width: 100%;
    transition: all 0.25s ease-in-out;
    position: relative;
    max-height: 75vh;
}

#heroVideoBtn {
    z-index: 2;
    transition: all 0.25s ease-in-out;
    cursor: pointer;
}

#heroVideoBtn:hover {
    transform: translateY(-2px);
    transition: all 0.25s ease-in-out;
}

#heroVideoBtn.hide {
    opacity: 0;
    cursor: unset;
}

.hero__video video {
    height: 100%;
    width: 100%;
    object-position: center;
    object-fit: cover;
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
}

.hero[data-phase="1"] .hero__copy {
    grid-column: 1 / 7;
    grid-row: 1 / 6;
    z-index: 2;
    height: 100%;
}

.hero__copy p {
    margin: 0;
}

.hero__image--container {
    z-index: 1;
    grid-column: 1 / 13;
    grid-row: 1 / 13;
    width: 100%;
    height: 100%;

}

.hero__image--container img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: bottom;
}

.hero__image[data-screen="desktop"] {
    display: block;
}

.hero__image[data-screen="tablet"] {
    display: none;
}

.hero__image[data-screen="mobile"] {
    display: none;
}

#instagram {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;

    overflow-x: hidden;
}

#instagram h2 {
    /* text-transform: unset; */
}

.instagram__swiper {
    height: 296px;
    width: 100%;
    overflow-x: hidden;
}

.instagram__swiper .swiper-slide, .instagram__swiper .swiper-slide a {
    height: 296px;
    width: 296px;
    background: #909DA3;
    color: #fff;

    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
}

.instagram__swiper .swiper-slide img, .instagram__swiper .swiper-slide a img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.instagram__link {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 25px;
}

.instagram__link h2 {
    margin-bottom: 0;
}

.design {
    display: flex;
    flex-flow: row nowrap;
    row-gap: 30px;
}


.design__copy {
    grid-column: 1 / 7;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: flex-start;
}

.design__copy h2 {
    margin-bottom: 10px;
}

.design__copy p {
    margin-bottom: 45px;
}

.design__image {
    flex: 1 0 48%;
    max-height: min-content;
}

.design__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.design__image img[data-screen="desktop"] {
    display: block;
}

.design__image img[data-screen="tablet"] {
    display: none;
}

.testimonials__content {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.testimonials__item {
    flex: 1;
    
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.testimonials__item h2 {
    /* text-transform: unset; */
}

.btn {
    font-family: "Cormorant Garamond", serif;
    font-weight: 400;

    background: #A15D3C;
    color: #fff;
    /* text-transform: uppercase; */

    font-size: 20px;
    line-height: 24px;
    letter-spacing: 2px;

    padding: 10px 20px;

    border: none;

    transition: all 0.25s ease-in-out;

    user-select: none;
}

.btn:hover {
    background: #915436;
}

nav[data-color="beige"] {
    background: #FFF8EF;
    color: #1A272C;
}

section[data-color="beige"] {
    background: #FFF8EF;
    color: #1A272C;
}

section[data-color="slate"] {
    background: #909DA3;
    color: #fff;
}

section[data-color="grey"] {
    background: #1A272C;
    color: #FFF8EF;
}

.testimonials {
    display: flex;
    flex-flow: row nowrap;
    row-gap: 30px;
    column-gap: 30px;
}

.testimonials__icon {
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.swiper {
    padding-bottom: 50px;
}

.noscroll {
    overflow-y: hidden;
}

.testimonials__icon img,
.caseStudy__testimonials--icon img {
    width: 100px;
}

.testimonials__container,
.caseStudy__testimonials--item {
    display: flex;
    flex-flow: row nowrap;
    column-gap: 30px;
    row-gap: 30px;
}

.contact {
    background: url('assets/images/contact-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 175px;
}

.contact__container {
    background: rgba(26, 39, 44, 0.9);
    color: #FFF8EF;
    padding: 110px 75px;
    display: flex;
    flex-flow: column nowrap;
    column-gap: 40px;
    row-gap: 40px;
    max-width: 750px;
}

.contact h2 {
    font-size: 48px;
    line-height: 58px;
    letter-spacing: 3.5px;
    margin: 0;
}

.contact p {
    margin: 0;
}

.contact__form {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
    row-gap: 30px;
    column-gap: 30px;
    width: 100%;
}

.contact__item {
    flex: 1 0 45%;
    height: 40px;
    min-width: 250px;
    transition: all 0.25s ease-in-out;
}

.contact__item--input[disabled] {
    background: #ffffff10;
    transition: all 0.25s ease-in-out;
}

#submitForm.success {
    background: green;
    transition: all 0.25s ease-in-out;
}

#submitForm.error {
    background: red;
    transition: all 0.25s ease-in-out;
}

.contact__item--message {
    height: 100px;
}

.contact input,
.contact textarea {
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-size: 12px;
    line-height: 0;
    /* text-transform: uppercase; */
    letter-spacing: 1px;
    background: #ffffff30;
    color: #FFF8EF;
    border: 0;
    padding: 15px;

    resize: none;
    padding: 15px;
    width: 100%;
    height: 100%;

    user-select: auto;
}

.contact input::placeholder,
.contact textarea::placeholder {
    letter-spacing: 1px;
    color: #FFF8EF;
}

.contact button {
    font-size: 13px;
    letter-spacing: 3.5px;
    width: 100%;

    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 10px;
}

#footer {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    column-gap: 30px;
    row-gap: 30px;
    text-align: center;
    grid-column: 1 / 13;
}

.footer__items {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    row-gap: 10px;
    grid-column: 1 / 13;
    z-index: 1;
    grid-row: 1 / 2;
}

.footer__return {
    display: flex;
    justify-self: center;
    align-items: center;
    transition: all 0.25s ease-in-out;
    grid-column: 12 / 13;
    grid-row: 1 / 2;
    height: 100%;
    z-index: 2;
}

.footer__return:hover {
    transform: translateY(-2px);
    transition: all 0.25s ease-in-out;
}

@media screen and (max-width: 1600px) {
    .hero[data-phase="1"] {
        max-height: 900px;
    }

    .hero[data-phase="1"] .hero__copy {
        grid-column: 1 / 8;
    }
}

@media screen and (max-width: 1366px) {
    .hero[data-phase="1"] {
        padding-top: 0;
    }

    .hero[data-phase="1"] .hero__copy {
        grid-column: 1 / 9;
    }

    .banner h3 {
        font-size: 30px !important;
        line-height: 36px !important;
        margin: 0;
    }

    .gc { 
        /* grid-template-columns: repeat(12, 1fr); */
        grid-template-columns: 80px repeat(10, 1fr) 80px;
    }

    .contact {
        padding: 100px;
    }

    .contact__container {
        padding: 100px;
    }
}

@media screen and (max-width: 1200px) {
    .hero[data-phase="1"] .hero__copy {
        grid-column: 1 / 10;
    }
}

@media screen and (max-width: 1024px) {
    .parallax {  
        height: inherit;
        background-attachment: unset !important;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }
      
    h2 {
        font-size: 24px;
        line-height: 30px;
    }

    .contactBtn[data-type="nav"] {
        display: none;
    }

    .nav {
        padding: 25px;
    }

    .nav__menu, .expand__menu {
        padding: 25px;
        width: 340px;
    }

    .nav__item, .nav__item--section, .nav__item--sub {
        font-size: 20px;
        line-height: 24px;
    }

    .nav__item[data-direction="vertical"] {
        font-size: 20px;
        line-height: 24px;
    }

    .hero[data-phase="1"] {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
    
        max-height: unset;
        height: unset;
        min-height: unset;
    }

    .hero__video {
        max-height: 400px;
    }

    .hero__image[data-screen="desktop"] {
        display: none;
    }

    .hero__image[data-screen="tablet"] {
        display: block;
    }

    .design {
        flex-flow: column-reverse nowrap;
    }

    .design__image img[data-screen="desktop"] {
        display: none;
    }

    .design__image img[data-screen="tablet"] {
        display: block;
    }

    .testimonials {
        flex-flow: row wrap;
    }

    .testimonials__content {
        display: flex;
        flex-flow: column nowrap;
    }

    .contact h2 {
        font-size: 27px;
        line-height: 33px;
        letter-spacing: 1px;
    }

    .testimonials__icon {
        grid-row: 1 / 2;
        grid-column: 2 / 13;
    }
    
    .testimonials__container,
    .caseStudy__testimonials--item {
        grid-row: 2 / 3;
        grid-column: 2 / 13;
        flex-flow: column nowrap;
    }

    .contact {
        padding: 0;
    }

    .contact__container {
        padding: 40px;
    }

    .sp {
        padding: 50px 35px;
    }

    .sp-b {
        padding-bottom: 50px;
    }

    .sp-t {
        padding-top: 50px;
    }

    .sp-y {
        padding-left: 35px;
        padding-right: 35px;    
    }

    .sp-l {
        padding-left: 35px;
    }

    .sp-x {
        padding-top: 50px;
        padding-bottom: 50px;
    }
}

.contactPopup {
    background: rgba(26, 39, 44, 0.9);
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
    z-index: -1000;
    box-sizing: border-box;

    display: none;
    flex-flow: column nowrap;
    /* justify-content: center; */
    /* align-items: center; */

    padding: 50px;

    animation: FadeIn 0.25s ease-in-out;
}

@media screen and (max-width: 800px) {
    .nav__projects {
        display: none;
    }
    
    .banner  h3 {
        font-size: 18px !important;
        line-height: 20px !important;
    }

    #footer {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
    }

    .contactPopup {
        padding: 0;
    }
}

@media screen and (max-width: 600px) {
    h1 {
        font-size: 35px;
        line-height: 40px;
        margin-bottom: 25px;
    }

    h3 {
        font-size: 24px;
        line-height: 30px;
        margin-bottom: 20px;
    }

    p {
        font-size: 16px;
        line-height: 20px;
    }

    .gc { 
        /* grid-template-columns: repeat(12, 1fr); */
        grid-template-columns: 35px repeat(10, 1fr) 35px;
    }

    .nav {
        padding: 10px;
        height: 75px;
    }

    .nav__wrapper {
        height: 75px;
    }

    .nav__item {
        font-size: 18px;
        line-height: 22px;
        user-select: none;
    }

    .nav__item[data-direction="vertical"] {
        font-size: 18px;
        line-height: 22px;
    }

    .hero__image[data-screen="tablet"] {
        display: none;
    }

    .hero__image[data-screen="mobile"] {
        display: block;
    }
} 

.contactPopup.active {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    display: flex;

    animation: FadeIn 0.25s ease-in-out;
}

.contactPopup__close {
    height: 25px;
    width: 100vw;

    position: absolute;
    right: 0;
    top: 0;
    padding: 25px;

    display: flex;
    justify-content: flex-end;
    align-items: center;
    cursor: pointer;
}

.contactPopup__close img {
    height: 25px;
}

.contactPopup .contact__container {
    background: rgb(26, 39, 44);
    padding: 50px;
}

@keyframes FadeIn {
    from {
        opacity: 0;
    } to {
        opacity: 1;
    }
}