        document.addEventListener('DOMContentLoaded', () => {
            let isScrolling = false;
            let disableScrolling = false;

            if (window.scrollY > 200) disableScrolling = true;

            !disableScrolling && document.addEventListener('scroll', (e) => {
                if (!disableScrolling) {
                    e.preventDefault();

                    if (isScrolling) {
                        return false;
                    } else {
                        isScrolling = true;
                        disableScrolling = true;

                        console.log('scroll')
                        const scroll = document.getElementById('planningContent').getBoundingClientRect();     

                        window.scrollTo({
                            top: scroll.top,
                            behavior: "smooth",
                        });
                        
                        setTimeout(() => {
                            isScrolling = false;
                        }, 1000);
                    }
                }
            });
        });
        
        // Init sections
        let activeSection = 'commercial';

        const param = window.location.search.split('?c=')[1];
        const valid = ['commercial', 'commercial', 'heritage', 'community', 'multiunit', 'masterplanning'];
        if (valid.includes(param)) activeSection = param;

        const planningCategory = document.querySelectorAll('.planning__category');
        const categoryBtns = document.querySelectorAll('.planning__category--btn');
        const npi = document.querySelectorAll('.navPlanning__item');

        const initSection = (selected) => {
            activeSection = selected;

            planningCategory.forEach(el => {
                const attr = el.getAttribute('data-category');
                if (attr === selected) {
                    el.classList.add('active');
                } else {
                    el.classList.remove('active');
                }
            });

            categoryBtns.forEach(el => {
                const attr = el.getAttribute('data-category');
                if (attr === selected) {
                    el.classList.add('active');
                } else {
                    el.classList.remove('active');
                }
            });

            npi.forEach(el => {
                const attr = el.getAttribute('data-category');
                if (attr === selected) {
                    el.classList.add('active');
                } else {
                    el.classList.remove('active');
                }
            });
        }

        categoryBtns.forEach(el => el.addEventListener('click', (e) => {
            const attr = e.target.getAttribute('data-category');
            initSection(attr);
        }));

        initSection(activeSection);

        // Init popups
        const caseStudyPopups = document.querySelectorAll('.caseStudy');
        const caseStudyBtns = document.querySelectorAll('.caseStudyBtn');
        const body = document.querySelector('body');

        caseStudyBtns.forEach(btn => {
            const id = btn.getAttribute('data-casestudy');
            const popup = document.querySelector(`.${id}`);

            btn.addEventListener('click', () => {
                console.log('click')
                caseStudyPopups.forEach(p => {
                    p.classList.remove('active');
                    body.classList.remove('noscroll');
                });
                if (popup) {
                    popup.classList.add('active');
                    body.classList.add('noscroll');
                }
            })
        });

        // Init popups close
        const caseStudyClose = document.querySelectorAll('.caseStudy__close');
        caseStudyClose.forEach(btn => btn.addEventListener('click', () => caseStudyPopups.forEach(p => {
            p.classList.add('close');

            setTimeout(() => {
                p.classList.remove('active');
                body.classList.remove('noscroll');
                p.classList.remove('close');
            }, 750);
        })))

            // Init Swipers
    const swipers = document.querySelectorAll('.caseStudy__swiper');

    swipers.forEach((s, i) => {
        return (
            new Swiper(s, {
                autoplay: {
                    delay: 5000
                },
                slidesPerView: "auto",
                spaceBetween: 30,
                // centeredSlides: true,
                // centerMode: true,
                loop: true,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.caseStudy__swiper--pagination',
                    type: 'bullets',
                    clickable: true
                },
            })
    )
    });