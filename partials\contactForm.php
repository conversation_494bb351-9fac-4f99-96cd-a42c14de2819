<form class="cw-xl contact__form" name="contact">
    <div class="contact__item">
        <input class="contact__item--input" id="firstName" name="firstName" type="text" placeholder="First Name" />
    </div>

    <div class="contact__item">
        <input class="contact__item--input" id="lastName" name="lastName" type="text" placeholder="Last Name" />
    </div>

    <div class="contact__item">
        <input class="contact__item--input" id="phoneNumber" name="phoneNumber" type="text" placeholder="Phone Number" />
    </div>

    <div class="contact__item">
        <input class="contact__item--input" id="email" name="email" type="email" placeholder="Email" required />
    </div>

    <div class="contact__item contact__item--message">
        <input class="contact__item--input" id="message" name="message" placeholder="Message" />
    </div>
</form>

<button class="btn contact__item--input" id="submitForm">
    <span id="submitFormStatus">Send</span>
    <img src="/vwarchitects/assets/svg/arrow.svg" />
</button>

<script>
    const submitBtn = document.querySelector("#submitForm");
    const submitBtnStatus = document.querySelector("#submitFormStatus");
    const contactItemInputs = document.querySelectorAll('.contact__item--input');

    contactItemInputs.forEach(i => i.addEventListener('input', () => {
        if (submitBtnStatus.textContent != 'Send') {
            submitBtnStatus.textContent = 'Send';
        }

        submitForm.classList.remove('error');
    }));

    submitBtn.addEventListener('click', () => {
        const firstName =  document.querySelector('#firstName').value ? document.querySelector('#firstName').value : 'Not provided';
        const lastName =  document.querySelector('#lastName').value ? document.querySelector('#lastName').value : 'Not provided';
        const phoneNumber =  document.querySelector('#phoneNumber').value ? document.querySelector('#phoneNumber').value : 'Not provided';
        const email =  document.querySelector('#email').value;
        const message =  document.querySelector('#message').value ? document.querySelector('#message').value : 'Not provided';

        if (!email.match(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)) {
            submitBtnStatus.textContent = 'Please provide an email';
            submitForm.classList.remove('success');
            submitForm.classList.add('error');
        } else {
            submitBtnStatus.textContent = 'Submitting';
            contactItemInputs.forEach(input => input.disabled = true);

            const url = '/vwarchitects/php/contactForm.php';

            const data = {
                firstName,
                lastName,
                phoneNumber,
                email,
                message
            }

            $.post(url, data, function (data) {
                submitBtnStatus.textContent = 'Thank you! Form received';
                submitForm.classList.add('success');

                setTimeout(() => {
                    contactItemInputs.forEach(input => input.disabled = false);
                    submitBtnStatus.textContent = 'Send';
                    submitForm.classList.remove('success');
                }, 5000);

                contactItemInputs.forEach(input => input.value = '');
            }).fail(err => {
                console.error('AJAX Error:', err);
                submitBtnStatus.textContent = 'Something went wrong... <NAME_EMAIL>';
                submitForm.classList.remove('success');
                submitForm.classList.add('error');
                contactItemInputs.forEach(input => input.disabled = false);
            });
        }
    });
</script>