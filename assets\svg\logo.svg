<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="498" height="47.563" viewBox="0 0 498 47.563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_220" data-name="Rectangle 220" width="498" height="47.563" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_250" data-name="Group 250" transform="translate(0 0)">
    <g id="Group_249" data-name="Group 249" transform="translate(0 0)" clip-path="url(#clip-path)">
      <path id="Path_464" data-name="Path 464" d="M50.372,43.068v7.753H37.04a13.459,13.459,0,0,1,13.332-7.753" transform="translate(-2.807 -3.258)" fill="#2c4f65"/>
      <path id="Path_465" data-name="Path 465" d="M49.681,30.522v7.784c-6.553.258-13.651,5.419-15.917,11.57H27.925C30.38,40.5,40.085,31.869,49.681,30.522" transform="translate(-2.116 -2.313)" fill="#2c4f65"/>
      <path id="Path_466" data-name="Path 466" d="M49.15,11.121V22.136C38.734,25.354,28.072,36.611,25.442,47.194L20.912,38.4C24.7,26.8,37.434,14.5,49.15,11.121" transform="translate(-1.585 -0.843)" fill="#2c4f65"/>
      <path id="Path_467" data-name="Path 467" d="M0,0H47.567C35.292,5.789,21.7,21.543,17.773,34.528Z" transform="translate(0 0)" fill="#2c4f65"/>
      <path id="Path_468" data-name="Path 468" d="M19.983,42.738l4.15,8.064H18a46.82,46.82,0,0,1,1.986-8.064" transform="translate(-1.364 -3.238)" fill="#2c4f65"/>
      <path id="Path_469" data-name="Path 469" d="M0,3.592,17.079,36.773a65.332,65.332,0,0,0-2.432,9.782L0,18.1Z" transform="translate(0 -0.272)" fill="#2c4f65"/>
      <path id="Path_470" data-name="Path 470" d="M0,22.877,13.6,49.3H0V22.877Z" transform="translate(0 -1.733)" fill="#2c4f65"/>
      <path id="Path_471" data-name="Path 471" d="M85.8,11.026,76.08,36.794H72.2L62.475,11.026h3.586l8.1,22.218,8.1-22.218Z" transform="translate(-4.734 -0.835)" fill="#2c4f65"/>
      <path id="Path_472" data-name="Path 472" d="M90.612,21.561a9.126,9.126,0,0,1,3.457-3.641,9.484,9.484,0,0,1,4.9-1.294,9,9,0,0,1,4.621,1.146,7.64,7.64,0,0,1,2.921,2.884v-3.7h3.4V37.218h-3.4V33.447a7.917,7.917,0,0,1-2.976,2.939,8.893,8.893,0,0,1-4.6,1.165,9.185,9.185,0,0,1-4.88-1.332,9.328,9.328,0,0,1-3.438-3.734,11.633,11.633,0,0,1-1.257-5.471,11.388,11.388,0,0,1,1.257-5.453m14.972,1.5a6.53,6.53,0,0,0-2.5-2.605,7.073,7.073,0,0,0-6.895-.019,6.449,6.449,0,0,0-2.477,2.588,8.235,8.235,0,0,0-.924,3.993,8.423,8.423,0,0,0,.924,4.048,6.532,6.532,0,0,0,2.477,2.625,6.693,6.693,0,0,0,3.438.906,6.791,6.791,0,0,0,3.457-.906,6.494,6.494,0,0,0,2.5-2.625,8.34,8.34,0,0,0,.924-4.011,8.237,8.237,0,0,0-.924-3.993" transform="translate(-6.771 -1.26)" fill="#2c4f65"/>
      <path id="Path_473" data-name="Path 473" d="M132.615,18.823a8.611,8.611,0,0,1,2.292,6.451V37.215H131.58V25.754a6.518,6.518,0,0,0-1.516-4.64,5.417,5.417,0,0,0-4.141-1.608,5.548,5.548,0,0,0-4.233,1.664,6.794,6.794,0,0,0-1.571,4.843v11.2h-3.364V16.956h3.364V19.84a6.679,6.679,0,0,1,2.717-2.4,8.443,8.443,0,0,1,3.789-.85,8.218,8.218,0,0,1,5.989,2.237" transform="translate(-8.847 -1.257)" fill="#2c4f65"/>
      <path id="Path_474" data-name="Path 474" d="M141.252,21.007a9.126,9.126,0,0,1,3.457-3.641,9.55,9.55,0,0,1,4.935-1.294,9.163,9.163,0,0,1,4.4,1.092,7.865,7.865,0,0,1,3.105,2.864V9.307h3.4V36.663h-3.4V32.855a7.872,7.872,0,0,1-2.958,2.976A8.8,8.8,0,0,1,149.607,37a9.22,9.22,0,0,1-8.355-5.066A11.634,11.634,0,0,1,140,26.46a11.389,11.389,0,0,1,1.257-5.453m14.972,1.5a6.53,6.53,0,0,0-2.5-2.605,7.073,7.073,0,0,0-6.895-.019,6.449,6.449,0,0,0-2.477,2.588,8.235,8.235,0,0,0-.924,3.993,8.423,8.423,0,0,0,.924,4.048,6.533,6.533,0,0,0,2.477,2.625,6.693,6.693,0,0,0,3.438.906,6.791,6.791,0,0,0,3.457-.906,6.494,6.494,0,0,0,2.5-2.625,8.34,8.34,0,0,0,.924-4.011,8.238,8.238,0,0,0-.924-3.993" transform="translate(-10.608 -0.705)" fill="#2c4f65"/>
      <path id="Path_475" data-name="Path 475" d="M185.664,28.346H169.472a6.367,6.367,0,0,0,6.58,6.359,6.212,6.212,0,0,0,3.641-1.018,5.381,5.381,0,0,0,2.052-2.716h3.623a8.716,8.716,0,0,1-3.253,4.75,9.82,9.82,0,0,1-6.063,1.83,10.244,10.244,0,0,1-5.157-1.294,9.155,9.155,0,0,1-3.567-3.678,11.4,11.4,0,0,1-1.294-5.527,11.568,11.568,0,0,1,1.257-5.508,8.826,8.826,0,0,1,3.531-3.642,10.508,10.508,0,0,1,5.231-1.275,10.165,10.165,0,0,1,5.1,1.258,8.705,8.705,0,0,1,3.42,3.457,10.2,10.2,0,0,1,1.2,4.972,19.8,19.8,0,0,1-.111,2.033M181.45,22.3a5.529,5.529,0,0,0-2.311-2.107,7.2,7.2,0,0,0-3.235-.722,6.236,6.236,0,0,0-4.344,1.627,6.626,6.626,0,0,0-2.052,4.511H182.3a6.208,6.208,0,0,0-.85-3.309" transform="translate(-12.582 -1.26)" fill="#2c4f65"/>
      <path id="Path_476" data-name="Path 476" d="M207.055,18.823a8.61,8.61,0,0,1,2.292,6.451V37.215h-3.328V25.754a6.514,6.514,0,0,0-1.516-4.64,5.414,5.414,0,0,0-4.14-1.608,5.547,5.547,0,0,0-4.233,1.664,6.79,6.79,0,0,0-1.571,4.843v11.2H191.2V16.956h3.364V19.84a6.679,6.679,0,0,1,2.717-2.4,8.436,8.436,0,0,1,3.788-.85,8.22,8.22,0,0,1,5.99,2.237" transform="translate(-14.488 -1.257)" fill="#2c4f65"/>
      <path id="Path_477" data-name="Path 477" d="M222.19,17.217a8.974,8.974,0,0,1,4.547-1.146,9.458,9.458,0,0,1,4.917,1.294,9.176,9.176,0,0,1,3.438,3.641,11.389,11.389,0,0,1,1.257,5.453,11.634,11.634,0,0,1-1.257,5.471A9.22,9.22,0,0,1,226.737,37a9,9,0,0,1-4.6-1.147,7.783,7.783,0,0,1-2.976-2.921v3.734h-3.364V9.307h3.364V20.176a7.952,7.952,0,0,1,3.031-2.958m9.8,5.25a6.413,6.413,0,0,0-2.5-2.588,6.919,6.919,0,0,0-3.457-.887,6.721,6.721,0,0,0-3.42.907,6.61,6.61,0,0,0-2.514,2.625,8.13,8.13,0,0,0-.943,3.973,8.21,8.21,0,0,0,.943,4.011,6.61,6.61,0,0,0,2.514,2.625,6.721,6.721,0,0,0,3.42.907,6.794,6.794,0,0,0,3.457-.907,6.5,6.5,0,0,0,2.5-2.625,8.434,8.434,0,0,0,.924-4.048,8.248,8.248,0,0,0-.924-3.993" transform="translate(-16.352 -0.705)" fill="#2c4f65"/>
      <path id="Path_478" data-name="Path 478" d="M260.1,28.346H243.912a6.368,6.368,0,0,0,6.581,6.359,6.212,6.212,0,0,0,3.641-1.018,5.381,5.381,0,0,0,2.052-2.716h3.623a8.716,8.716,0,0,1-3.253,4.75,9.82,9.82,0,0,1-6.063,1.83,10.244,10.244,0,0,1-5.157-1.294,9.155,9.155,0,0,1-3.567-3.678,11.406,11.406,0,0,1-1.294-5.527,11.568,11.568,0,0,1,1.257-5.508,8.826,8.826,0,0,1,3.531-3.642,10.508,10.508,0,0,1,5.231-1.275,10.165,10.165,0,0,1,5.1,1.258,8.705,8.705,0,0,1,3.42,3.457,10.2,10.2,0,0,1,1.2,4.972,19.789,19.789,0,0,1-.111,2.033M255.89,22.3a5.528,5.528,0,0,0-2.31-2.107,7.2,7.2,0,0,0-3.235-.722A6.236,6.236,0,0,0,246,21.1a6.626,6.626,0,0,0-2.052,4.511H256.74a6.207,6.207,0,0,0-.85-3.309" transform="translate(-18.222 -1.26)" fill="#2c4f65"/>
      <path id="Path_479" data-name="Path 479" d="M271.531,17.547a7.839,7.839,0,0,1,4.011-.961v3.476h-.887Q269,20.062,269,26.2V37.215h-3.364V16.956H269v3.29a6.456,6.456,0,0,1,2.532-2.7" transform="translate(-20.129 -1.257)" fill="#2c4f65"/>
      <path id="Path_480" data-name="Path 480" d="M292.408,17.772a7.615,7.615,0,0,1,2.94,2.884v-3.7h3.4v20.7a10.112,10.112,0,0,1-1.184,4.935,8.439,8.439,0,0,1-3.383,3.383,10.382,10.382,0,0,1-5.119,1.219,11.226,11.226,0,0,1-6.654-1.885,7.365,7.365,0,0,1-3.142-5.138h3.327a5.289,5.289,0,0,0,2.292,2.975,7.5,7.5,0,0,0,4.177,1.128,6.166,6.166,0,0,0,4.529-1.738,6.561,6.561,0,0,0,1.756-4.879V33.41a8.1,8.1,0,0,1-2.958,2.958,8.7,8.7,0,0,1-4.584,1.184,9.22,9.22,0,0,1-8.355-5.066,11.622,11.622,0,0,1-1.258-5.471,11.378,11.378,0,0,1,1.258-5.453,9.12,9.12,0,0,1,3.457-3.641,9.481,9.481,0,0,1,4.9-1.294,9.02,9.02,0,0,1,4.6,1.146m2.016,5.287a6.529,6.529,0,0,0-2.5-2.605,7.073,7.073,0,0,0-6.895-.019,6.443,6.443,0,0,0-2.477,2.588,8.235,8.235,0,0,0-.924,3.993,8.423,8.423,0,0,0,.924,4.048,6.527,6.527,0,0,0,2.477,2.625,6.693,6.693,0,0,0,3.438.906,6.793,6.793,0,0,0,3.457-.906,6.494,6.494,0,0,0,2.5-2.625,8.35,8.35,0,0,0,.924-4.011,8.247,8.247,0,0,0-.924-3.993" transform="translate(-21.081 -1.26)" fill="#2c4f65"/>
      <path id="Path_481" data-name="Path 481" d="M333.131,36.751l-3.365-3.4a12.715,12.715,0,0,1-4.029,2.9,11.919,11.919,0,0,1-4.843.944,11.015,11.015,0,0,1-4.714-.961A7.363,7.363,0,0,1,313,33.5a7.551,7.551,0,0,1-1.128-4.141,7.717,7.717,0,0,1,1.533-4.75,10.1,10.1,0,0,1,4.529-3.235,10.988,10.988,0,0,1-1.7-2.589,6.37,6.37,0,0,1-.518-2.588,5.382,5.382,0,0,1,.832-2.938,5.575,5.575,0,0,1,2.385-2.052,8.259,8.259,0,0,1,3.586-.74,7.36,7.36,0,0,1,3.475.777,4.8,4.8,0,0,1,2.067,2.163,5.883,5.883,0,0,1,.665,3.087h-3.212a3.081,3.081,0,0,0-.85-2.385,3.143,3.143,0,0,0-2.293-.869,3.392,3.392,0,0,0-2.385.832,2.76,2.76,0,0,0-.9,2.126,4.163,4.163,0,0,0,.61,2.145,15.05,15.05,0,0,0,2.052,2.55l7.69,7.727.665-1.035,2.513-4.215h3.623L333.2,28.619q-.628,1.108-1.479,2.328l5.805,5.8ZM324.5,33.535a9.844,9.844,0,0,0,3.051-2.4l-7.579-7.616q-4.733,1.813-4.733,5.73a4.706,4.706,0,0,0,1.572,3.623,5.8,5.8,0,0,0,4.085,1.443,8.167,8.167,0,0,0,3.6-.777" transform="translate(-23.633 -0.793)" fill="#2c4f65"/>
      <path id="Path_482" data-name="Path 482" d="M318.434,21.833c1.462-.676,3.186-1.115,6.044-2.408a2.425,2.425,0,0,0,.684-.5,3.072,3.072,0,0,0,.85-2.384h3.213a5.9,5.9,0,0,1-.666,3.087,4.455,4.455,0,0,1-2.094,2.022,39.549,39.549,0,0,1-6,2.327" transform="translate(-24.13 -1.253)" fill="#2c4f65"/>
      <path id="Path_483" data-name="Path 483" d="M380.334,11.026l-7.468,25.767H369.1l-5.989-20.74L356.9,36.794l-3.734.037-7.209-25.8h3.586l5.619,21.849,6.21-21.849h3.772L371.054,32.8l5.656-21.775Z" transform="translate(-26.215 -0.835)" fill="#2c4f65"/>
      <path id="Path_484" data-name="Path 484" d="M386.478,12.651a1.956,1.956,0,0,1,1.387-3.344,1.8,1.8,0,0,1,1.341.568,2.018,2.018,0,0,1,0,2.775,1.8,1.8,0,0,1-1.341.567,1.885,1.885,0,0,1-1.387-.567M389.5,36.663h-3.364V16.4H389.5Z" transform="translate(-29.243 -0.705)" fill="#2c4f65"/>
      <rect id="Rectangle_219" data-name="Rectangle 219" width="3.364" height="27.357" transform="translate(365.061 8.601)" fill="#2c4f65"/>
      <path id="Path_485" data-name="Path 485" d="M403.729,21.007a9.12,9.12,0,0,1,3.456-3.641,9.551,9.551,0,0,1,4.935-1.294,9.163,9.163,0,0,1,4.4,1.092,7.872,7.872,0,0,1,3.105,2.864V9.307h3.4V36.663h-3.4V32.855a7.879,7.879,0,0,1-2.957,2.976A8.8,8.8,0,0,1,412.084,37a9.219,9.219,0,0,1-8.355-5.066,11.622,11.622,0,0,1-1.258-5.471,11.378,11.378,0,0,1,1.258-5.453M418.7,22.5a6.53,6.53,0,0,0-2.5-2.605,7.073,7.073,0,0,0-6.895-.019,6.442,6.442,0,0,0-2.477,2.588,8.235,8.235,0,0,0-.924,3.993,8.423,8.423,0,0,0,.924,4.048,6.525,6.525,0,0,0,2.477,2.625,6.693,6.693,0,0,0,3.438.906,6.793,6.793,0,0,0,3.456-.906,6.5,6.5,0,0,0,2.5-2.625,8.351,8.351,0,0,0,.924-4.011A8.248,8.248,0,0,0,418.7,22.5" transform="translate(-30.498 -0.705)" fill="#2c4f65"/>
      <path id="Path_486" data-name="Path 486" d="M448.142,28.346H431.95a6.367,6.367,0,0,0,6.58,6.359,6.215,6.215,0,0,0,3.642-1.018,5.38,5.38,0,0,0,2.051-2.716h3.623a8.719,8.719,0,0,1-3.252,4.75,9.823,9.823,0,0,1-6.064,1.83,10.242,10.242,0,0,1-5.156-1.294,9.142,9.142,0,0,1-3.568-3.678,11.4,11.4,0,0,1-1.295-5.527,11.557,11.557,0,0,1,1.258-5.508A8.82,8.82,0,0,1,433.3,17.9a10.5,10.5,0,0,1,5.23-1.275,10.169,10.169,0,0,1,5.1,1.258,8.712,8.712,0,0,1,3.42,3.457,10.209,10.209,0,0,1,1.2,4.972,19.772,19.772,0,0,1-.111,2.033M443.928,22.3a5.529,5.529,0,0,0-2.311-2.107,7.2,7.2,0,0,0-3.235-.722,6.234,6.234,0,0,0-4.344,1.627,6.625,6.625,0,0,0-2.052,4.511h12.791a6.215,6.215,0,0,0-.85-3.309" transform="translate(-32.471 -1.26)" fill="#2c4f65"/>
      <path id="Path_487" data-name="Path 487" d="M460.067,17.217a8.977,8.977,0,0,1,4.547-1.146,9.461,9.461,0,0,1,4.918,1.294,9.174,9.174,0,0,1,3.437,3.641,11.378,11.378,0,0,1,1.258,5.453,11.622,11.622,0,0,1-1.258,5.471A9.219,9.219,0,0,1,464.614,37a9,9,0,0,1-4.6-1.147,7.786,7.786,0,0,1-2.977-2.921v3.734h-3.364V9.307h3.364V20.176a7.953,7.953,0,0,1,3.031-2.958m9.8,5.25a6.4,6.4,0,0,0-2.495-2.588,6.922,6.922,0,0,0-3.457-.887,6.725,6.725,0,0,0-3.421.907,6.608,6.608,0,0,0-2.513,2.625,8.121,8.121,0,0,0-.944,3.973,8.2,8.2,0,0,0,.944,4.011,6.608,6.608,0,0,0,2.513,2.625,6.725,6.725,0,0,0,3.421.907,6.8,6.8,0,0,0,3.457-.907,6.486,6.486,0,0,0,2.495-2.625,8.422,8.422,0,0,0,.924-4.048,8.237,8.237,0,0,0-.924-3.993" transform="translate(-34.378 -0.705)" fill="#2c4f65"/>
      <path id="Path_488" data-name="Path 488" d="M483.286,36.257a9.216,9.216,0,0,1-3.623-3.678,11.286,11.286,0,0,1-1.312-5.527,10.962,10.962,0,0,1,1.349-5.491,9.31,9.31,0,0,1,3.679-3.66,11.289,11.289,0,0,1,10.425,0,9.339,9.339,0,0,1,3.678,3.642,10.928,10.928,0,0,1,1.35,5.508,10.8,10.8,0,0,1-1.386,5.527,9.61,9.61,0,0,1-3.752,3.678,10.766,10.766,0,0,1-5.25,1.294,10.377,10.377,0,0,1-5.157-1.294m8.559-2.514a6.464,6.464,0,0,0,2.568-2.551,8.166,8.166,0,0,0,.981-4.141,8.308,8.308,0,0,0-.961-4.141,6.316,6.316,0,0,0-2.514-2.531,7.258,7.258,0,0,0-6.748,0,6.1,6.1,0,0,0-2.458,2.531,8.57,8.57,0,0,0-.924,4.141,8.782,8.782,0,0,0,.906,4.177,6.01,6.01,0,0,0,2.421,2.533,6.8,6.8,0,0,0,3.326.832,7.111,7.111,0,0,0,3.4-.85" transform="translate(-36.248 -1.26)" fill="#2c4f65"/>
      <path id="Path_489" data-name="Path 489" d="M522.582,28.346H506.39a6.367,6.367,0,0,0,6.58,6.359,6.215,6.215,0,0,0,3.642-1.018,5.378,5.378,0,0,0,2.051-2.716h3.623a8.719,8.719,0,0,1-3.252,4.75,9.822,9.822,0,0,1-6.064,1.83,10.243,10.243,0,0,1-5.156-1.294,9.144,9.144,0,0,1-3.568-3.678,11.4,11.4,0,0,1-1.295-5.527,11.558,11.558,0,0,1,1.258-5.508,8.821,8.821,0,0,1,3.531-3.642,10.5,10.5,0,0,1,5.23-1.275,10.168,10.168,0,0,1,5.1,1.258,8.712,8.712,0,0,1,3.42,3.457,10.21,10.21,0,0,1,1.2,4.972,19.806,19.806,0,0,1-.111,2.033M518.368,22.3a5.528,5.528,0,0,0-2.31-2.107,7.2,7.2,0,0,0-3.235-.722,6.234,6.234,0,0,0-4.344,1.627,6.625,6.625,0,0,0-2.052,4.511h12.791a6.215,6.215,0,0,0-.85-3.309" transform="translate(-38.112 -1.26)" fill="#2c4f65"/>
      <path id="Path_490" data-name="Path 490" d="M534.008,17.547a7.84,7.84,0,0,1,4.011-.961v3.476h-.887q-5.656,0-5.656,6.136V37.215h-3.364V16.956h3.364v3.29a6.457,6.457,0,0,1,2.532-2.7" transform="translate(-40.019 -1.257)" fill="#2c4f65"/>
    </g>
  </g>
</svg>