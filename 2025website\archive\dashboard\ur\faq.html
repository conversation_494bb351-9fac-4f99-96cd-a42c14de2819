<!doctype html>
<html lang="ur" dir="rtl">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all-rtl.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="ur ur_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/ur/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/ur/faq.html">اکثر پوچھے گئے سوالات</a></li>
              <li class="item "><a href="/dashboard/ur/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>عمومی سوالات</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>میں Mac OS X کے لئے XAMPP کی انسٹال کس طرح کروں؟</dt>
      <dd>
      <p>XAMPP کی انسٹال کرنے کے لئے صرف مندرجہ ذیل ہے:</p>
      <ul>
        <li>DMG-تصویر کو کھولنے.</li>
        <li>تنصیب کے عمل کو شروع کرنے کے لئے تصویر پر ڈبل کلک کریں.</li></ul>
      <p>وہ سب ہے. XAMPP کی اب / ایپلی کیشنز / XAMPP کی ڈائریکٹری کے نیچے نصب کیا جاتا ہے.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>میں کس طرح XAMPP کو سٹارٹ کروں ؟</dt>
      <dd>
      <p>XAMPP کی شروع کرنے کے لئے صرف XAMPP کی کنٹرول کھولنے اور Apache، MySQL اور ProFTPD شروع. XAMPP کی کنٹرول نام "مینیجر OSX" ہے.</p>      
      </dd>
      <dt>میں کس طرح XAMPP کو روک سکتا ہوں؟</dt>
      <dd>
      <p>XAMPP کی بس XAMPP کی کنٹرول کھولنے اور سرورز کو روکنے روکنے کے لئے. XAMPP کی کنٹرول نام "مینیجر OSX" ہے.</p>      
      </dd>
      <dt>میں کس طرح جانچ سکتا ہوں کہ سب چیزوں نے کام کیا ؟</dt>
      <dd>
      <p>ایک ویب براؤزر میں مندرجہ ذیل یو آر ایل میں ٹائپ کریں:</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>کیا XAMPP کی پیداوار تیار ہے؟</dt>
      <dd><p>XAMPP صرف ترقی کے ماحول کے لئے ہی نہیں ترتیب دیا گیا ہے، لیکن . XAMPP کو جس طرح وہ  چاہتا / چاہتی ہے ڈویلپر کچھ بھی اجازت دینے کے لئے ہر ممکن حد تک کھلا ہے. ترقی کے ماحول کے لئے یہ بہت اچھا ہے لیکن پیداوار کے ماحول میں یہ مہلک ہو سکتا ہے.</p>
      <p>یہاں XAMPP کی لاپتہ سلامتی کی فہرست:</p>
      <ol>
        <li>مائی ایس کیو ایل کے منتظم (جڑ)/ (root) کا کوئی پاسوڑد نہیں ہے.</li>
        <li>مائی ایس کیو ایل ڈیمان نیٹ ورک کے ذریعے قابل رسائی ہے.</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
      </ol>
      <p>بہت سی سیکورٹی کی کمزوریوں کو حل کرنے کے لئے صرف مندرجہ ذیل کمانڈ کو کال کریں:</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>یہ XAMPP کی تنصیب محفوظ بنانے کے لئے سیکورٹی چیک.</p></dd>

      <dt>xampp کی ابتداء میں دیکھی گئی خرابی کے پیغامات کے معنی کیا ہیں ؟</dt>
      <dd>
        <p>xampp کو شروع کرنے پر آپ کو کئی خرابی کے پیغامات موصول ہو سکتے ہیں:</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>LAMPP سکرپٹ نے XAMPP-Apache کو شروع نہیں کیا کیونکہ Apache کا ایک حصہ پہلے سے ہی چل رہا ہے.XAMPP کو باقاعدہ شروع کرنے کے لیے پہلے آپکو اس daemon کو بند کرنا پڈے گا.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>یہ بھی زیادہ تر اسی وجہ سے ہے جیسا کہ اوپر والی خرابی. LAMPP کے سٹارٹپ سکرپٹ نے آپکے سسٹم میں  مائی ایس کیو ایل daeman کو پہلے سے چلتا پایا.LAMPP کو باقاعدہ شروع کرنے کے لیے پہلے آپکو اس daemon کو بند کرنا پڈے گا.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>یہ خرابی ایک سے زیادہ وجوہات کی بنا پر موجود ہو سکتی ہے. اپاچی کئی حالات کے تحت یہ غلطی دکھاتا ہے. عین مطابق وجہ تلاش کرنے کے لیے ہمیں کچھ تحقیق کرنی ہے:</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>اگر آپکو کوئی خرابی کا پیغام ملتا ہے تو مدد کے لیے {community}% کا دورہ کریں.</p>
      </dd>

      <dt>میں کس طرح اپنے XAMPP کی انسٹالیشن زیادہ محفوظ بنا سکتا ہوں ؟</dt>
      <dd>
        <p>پہلے سے طے شدہ انسٹالیشن میں، XAMPP کا کوئی پاس ورڈ نہیں لگا ہوتا. XAMPP اس ترتیب سے دوسروں کے لیے قابل رسائی ہوتا ہے اس لی اسے ایسے چلانے کی سفارش نہیں کی جاتی ہے .</p>
        <p>بس ایک سادہ سیکورٹی چیک شروع کرنے کے لئے (جڑ کے طور پر) مندرجہ ذیل کمانڈ ٹائپ کریں:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>اب آپ کو آپ کی سکرین پر مندرجہ ذیل ڈائیلاگ دیکھنا چاہئے:</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) ایک پاس ورڈ لگانا XAMPP کی ڈیمو صفحات (/http://localhost/xampp) کی حفاظت کرے گا. استعمال کرنے کا نام 'lampp' ہے !</p>
        <p>اس حکم کو بلا نے کے بعد آپ کے XAMPP کی انسٹالیشن اور بھی زیادہ محفوظ ہونی چاہئے .</p>
      </dd>

      <dt>میں کس طرح PHP کے لئے OCI8/Oracle توسیع کو چالو کروں ؟</dt>
      <dd>
        <p>PHP کے لئے OCI8/Oracle توسیع چالو کرنے کے لئے مندرجہ ذیل کمانڈ کو چلایں :</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>مندرجہ ذیل ڈائیلاگ شروع ہو گا :</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>اب توسیع فعال ہونی چاہئے .</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
        <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
        <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
        <p>To enable remote access to phpMyAdmin, follow these steps:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>Within this file, find the lines below. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Then replace 'Require local' with 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Restart the Apache server using the XAMPP control panel.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>میں کس طرح بیک اپ/دوبارہ XAMPP نظام بحال کروں؟</dt>
      <dd>
        <p><strong>تنبیہ:</strong> بیک اپ اور فعالیت کو بحال ترقی کے تحت اب بھی ہے اور درست طریقے سے کام نہیں کر سکتے .</p>
        <p>آپ کو بلا کی طرف سے بیک اپ بنانے کے کر سکتے ہیں:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>یا</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>آپ "خفیہ" ایس کیو ایل کی جڑ پاس کہاں ہے. یہ کمانڈ مندرجہ ذیل پیداوار پیدا کر دے گا:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>فائل / Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh (مندرجہ بالا مثال میں) اپنے backuped کے اعداد و شمار پر مشتمل ہے. ایک کو بچانے کی جگہ پر اس فائل کو حاصل.</p>

        <p>نئی مشین پر آپ کو اپنے اصل / ذریعہ مشین کے طور پر xampp کی ورژن کی ضرورت ہے.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>آپ کچھ اس طرح نظر آنا چاہئے:</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>وہ سب ہے. یہ ایک بیٹا خصوصیات ہے کہ ذہن میں رکھیں.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">کاپی رائٹ 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">بلاگ</a></li>
            <li><a href="/privacy_policy.html">رازداری کی پالیسی</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN کی طرف سے فراہم
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
