#content {
    overflow-y: scroll;
}

#content::-webkit-scrollbar {
    width: 5px;
    /* height: 100vh; */
    background-color: #1A272C;
}

#content::-webkit-scrollbar-thumb {
    background: #A15D3C;
  }

.planning__container {
    display: grid;
    grid-template-columns: repeat(10, 1fr) 260px 80px;
    grid-template-rows: repeat(3, auto);
}

.planning__category {
    display: none !important;
    /* max-height: 100vh; */
    overflow-y: none;
}

.planning__category.active {
    display: block !important;
    width: 100%;
}

.planning__hero {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    
    grid-column: 1 / 2;
    grid-row: 1 / 2;
}

.navPlanning__item {
    color: #FFF8EF !important;
}

.planning__nav {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    row-gap: 40px;
    column-gap: 40px;
    /* grid-column: 12 / 13;
    grid-row: 3 / 4; */
    padding: 40px 0;
    /* max-height: 100vh; */
/*  */
}

.planning__container--nav {
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: center;
    grid-column: 12 / 13;
    grid-row: 1 / 4;
    row-gap: 50px;
    /* padding-top: 50px; */
    background-color: #1A272C;
}

.planning__container--nav .navPlanning__item {
    color: #fff;
}

.planning__category--btn {
    cursor: pointer;
    user-select: none;
}

.planning__hero--image {
    width: 100%;
    grid-column: 1 / 12;
    grid-row: 1 / 2;

    position: relative;
}

.planning__hero--overlay {
    width: 100%;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    height: 20%;
    background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
}

.planning__hero--image img {
    height: 790px;
    max-height: 75vh;
    width: 100%;
    object-fit: cover;
    object-position: center;
    box-sizing: border-box;
    z-index: 1;
}

.planning__hero--copy {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    align-items: center;
    column-gap: 30px;
    row-gap: 30px;
    grid-column: 1 / 11;
    grid-row: 2 / 3;
}

.planning__hero--copy h1 {
    flex: 0 1 30%;
}

.planning__hero--copy p {
    flex: 1 1 500px;
    margin: 0;
}

.projects {
    background: url('../assets/svg/watermark-bg.svg'), #1A272C;
    background-position: center;
    background-size: cover;
    background-repeat:repeat;
    grid-column: 1 / 12;
    grid-row: 3 / 4;
}

.project__item {
    display: flex;
    column-gap: 30px;
    row-gap: 30px;
    height: 735px;
    width: 100%;
    color: #fff;
}

.project__item:nth-of-type(odd) {
    flex-flow: row nowrap;
}
.project__item:nth-of-type(even) {
    flex-flow: row-reverse nowrap;
}

.projects__container {
    display: flex;
    flex-flow: column nowrap;
    column-gap: 30px;
    row-gap: 100px;
}

.project__image {
    flex: 1 1 73%;
}

.project__image img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.project__content {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: flex-start;
    row-gap: 30px;

    flex: 0 0 400px;
}

.project__content--copy {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    flex: 1;
    width: 100%;
}

.project__item:nth-of-type(even) {
    .project__content--copy {
    align-items: flex-end;
    text-align: right;
    }
}
.project__item:nth-of-type(odd) {
    .project__content--copy {
    align-items: flex-start;
    text-align: left;
    }
}

.project__content--image {
    max-height: 400px;
}

.project__content--image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
}


@media screen and (max-width: 1366px) {
    .planning__hero--image img {
        height: 445px;
    }

    .projects__container {
        row-gap: 75px;
    }

    .project__item {
        height: 600px;
    }

    .project__image {
        flex: 1 1 54%;
    }

    .project__content {
        flex: 0 0 300px;
    }
    
    .project__content--image {
        max-height: 300px;
    }
    
}

/* @media screen and (max-width: 1024px) {
    .planning__container--nav {
        display: none;
    }
} */

@media screen and (max-width: 1024px) {
    .planning__hero--image img {
        height: 289px;
    }

    .planning__container {
        grid-template-columns: repeat(11, 1fr) 40px;
        z-index: 5;
        position: relative;
    }

    .planning__category.active {
        z-index: 10;
    
    }

    .planning__hero--image {
        grid-column: 1 / 13;
        background: #FFF8EF;
    }

    .planning__hero--copy {
        grid-column: 1 / 13;
    }

    .project__content, .project__image {
        flex: 1 0 100%;
    }

    .project__image img {
        height: 268px;
    }

    .project__item {
        flex-flow: column nowrap !important;
        height: unset;
        row-gap: 15px;

    }

    .project__item h3, .project__item p {
        margin: 0;
    }

    .project__content--copy {
        row-gap: 15px;
    }

    .project__content--image {
        display: none;
    }

    #footer {
        z-index: 5;
        position: relative;
    }

}

.planning__hero--copy {
    z-index: 5;
    background: #FFF8EF;
}