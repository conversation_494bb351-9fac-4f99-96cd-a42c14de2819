<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="138" height="138" viewBox="0 0 138 138">
  <defs>
    <filter id="Ellipse_5" x="0" y="0" width="138" height="138" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_457" x="44.046" y="24.768" width="67.192" height="88.464" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Ellipse_5)">
    <g id="Ellipse_5-2" data-name="Ellipse 5" transform="translate(9 6)" fill="none" stroke="#fff8ef" stroke-width="5">
      <circle cx="60" cy="60" r="60" stroke="none"/>
      <circle cx="60" cy="60" r="57.5" fill="none"/>
    </g>
  </g>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Path_457)">
    <path id="Path_457-2" data-name="Path 457" d="M35.232,0,70.464,49.192H0Z" transform="translate(102.24 30.77) rotate(90)" fill="#fff8ef"/>
  </g>
</svg>
