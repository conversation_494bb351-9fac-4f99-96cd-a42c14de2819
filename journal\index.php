<?php 
    require_once( '../admin/cms.php' );
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
?>

<cms:template title="Journal Posts" clonable="1">
    <cms:editable name="journal__image" label="Journal image" type="image" />
    <cms:editable name="journal__header" label="Journal header" type="text" />
    <cms:editable name="journal__preview" label="Journal preview" type="text" />
    <cms:editable name="journal__content" label="Journal content" type="richtext" />
    
    <cms:folder name="projectspotlight" title="Project Spotlight" />
    <cms:folder name="recognition" title="Recognition" />
    <cms:folder name="generalnews" title="General News" />
</cms:template>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal | VW Architects</title>

    <?php include('../partials/headscripts.php') ?>

    <!-- CSS -->
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="journal.css">
</head>
<body>
    <div class="container" data-page="journal">
        <?php include('../partials/nav.php') ?>

        <cms:pages masterpage='journal/index.php' order='desc' paginate="1" limit="10">
            <!-- <cms:dump /> -->
        </cms:pages>

        <cms:if k_is_page>
            <div class="cw-xl sp" data-aos="fade">
                <h1><cms:show journal__header /></h1>
                <cms:show journal__content />
            </div>
        <cms:else />
            <div class="journal__hero">
                <div class="journal__hero--image">
                    <img src="../assets/images/article-01-img.jpg" data-aos="fade"/>
                </div>

                <div class="cw-xl sp journal__hero--copy">
                    <h2 data-aos="fade" data-aos-delay="100">Stay Current With VW Architects</h2>
                </div>
            </div>

            <section class="journal__wrapper" data-color="grey">
                <div class="cw-xl sp-t sp-b journal__content--nav">
                    <div class="journal__toggle nav__item active" data-direction="horizontial" data-category="projectspotlight" data-aos="fade" data-aos-delay="">Project Spotlight</div>
                    <div class="journal__toggle nav__item" data-direction="horizontial" data-category="recognition" data-aos="fade" data-aos-delay="50">Recognition</div>
                    <div class="journal__toggle nav__item" data-direction="horizontial" data-category="generalnews" data-aos="fade" data-aos-delay="100">General News</div>    
                </div>
            </section>

            <div class="journal__category active" data-category="projectspotlight">
                <section class="journal__wrapper" data-color="grey">
                        <div class="cw-xl sp-y journal__content--item" data-aos="fade" data-aos-delay="<cms:show k_count />00">
                            <cms:pages masterpage="journal/index.php" folder='projectspotlight' order='desc' limit="5">
                                <cms:if k_count = '1'>
                                    <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                    <div class="journal__content--copy">
                                        <h3><cms:show journal__header /></h3>
                                        <p><cms:show journal__preview /></p>
                                        <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                    </div>
                                </cms:if>
                            </cms:pages>
                        </div>
                </section>


                <div class="cw-xl sp journal__content">
                    <cms:pages masterpage="journal/index.php" folder='projectspotlight' order='desc' limit="5">
                        <cms:if k_count gt '1'>
                            <div class="journal__content--item">
                                <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                <div class="journal__content--copy">
                                    <h3><cms:show k_page_title /></h3>
                                    <p><cms:show journal__preview /></p>
                                    <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                </div>
                            </div>
                        </cms:if>
                    </cms:pages>
                </div>
            </div>

            <div class="journal__category" data-category="recognition">
                <section class="journal__wrapper" data-color="grey">
                        <div class="cw-xl sp-y journal__content--item">
                            <cms:pages masterpage="journal/index.php" folder='recognition' order='desc' limit="5">
                                <cms:if k_count = '1'>
                                    <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                    <div class="journal__content--copy">
                                        <h3><cms:show journal__header /></h3>
                                        <p><cms:show journal__preview /></p>
                                        <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                    </div>
                                </cms:if>
                            </cms:pages>
                        </div>
                </section>


                <div class="cw-xl sp journal__content">
                    <cms:pages masterpage="journal/index.php" folder='recognition' order='desc' limit="5">
                        <cms:if k_count gt '1'>
                            <div class="journal__content--item">
                                <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                <div class="journal__content--copy">
                                    <h3><cms:show k_page_title /></h3>
                                    <p><cms:show journal__preview /></p>
                                    <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                </div>
                            </div>
                        </cms:if>
                    </cms:pages>
                </div>
            </div>

            <div class="journal__category" data-category="generalnews">
                <section class="journal__wrapper" data-color="grey">
                        <div class="cw-xl sp-y journal__content--item">
                            <cms:pages masterpage="journal/index.php" folder='generalnews' order='desc' limit="5">
                                <cms:if k_count = '1'>
                                    <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                    <div class="journal__content--copy">
                                        <h3><cms:show journal__header /></h3>
                                        <p><cms:show journal__preview /></p>
                                        <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                    </div>
                                </cms:if>
                            </cms:pages>
                        </div>
                </section>


                <div class="cw-xl sp journal__content">
                    <cms:pages masterpage="journal/index.php" folder='generalnews' order='desc' limit="5">
                        <cms:if k_count gt '1'>
                            <div class="journal__content--item">
                                <div class="journal__content--image" style="background-image: url('<cms:show journal__image />')"></div>

                                <div class="journal__content--copy">
                                    <h3><cms:show k_page_title /></h3>
                                    <p><cms:show journal__preview /></p>
                                    <a href="<cms:show k_page_link />" class="btn">Continue Reading</a>
                                </div>
                            </div>
                        </cms:if>
                    </cms:pages>
                </div>
            </div>
        </cms:if>

        <?php include('../partials/footer.php') ?>

        <?php include('../partials/contactPopup.php') ?>
    </div>

    <script src="../script.js"></script>
    <script>
        if (document.querySelector('.instagram__swiper')) {
            new Swiper('.instagram__swiper', {
                autoplay: {
                    delay: 5000
                },
                slidesPerView: "auto",
                spaceBetween: 30,
                centeredSlides: true,
                loop: true,
            });
        }

        const jToggles = document.querySelectorAll('.journal__toggle')
        const jCategories = document.querySelectorAll('.journal__category');

        jToggles.forEach(t => t.addEventListener('click', () => {
            const c = t.getAttribute('data-category');
            jToggles.forEach(t => t.classList.remove('active'));

            jCategories.forEach(item => {
                const att = item.getAttribute('data-category');
                if (att === c) {
                    item.classList.add('active');
                    t.classList.add('active');
                } else {
                    item.classList.remove('active');
                    t.classList.remove('active');
                }
            })
        }))
    </script>

    <?php include('../partials/navActive.php') ?>
</body>
</html>
<?php COUCH::invoke(); ?>