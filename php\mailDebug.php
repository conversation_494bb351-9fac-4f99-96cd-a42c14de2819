<?php    
    header('Content-Type: text/html; charset=UTF-8');
    
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\Exception;
    use PHPMailer\PHPMailer\SMTP;

    require 'PHPMailer/Exception.php';
    require 'PHPMailer/PHPMailer.php';
    require 'PHPMailer/SMTP.php';

    echo "<h2>📧 邮件发送调试测试</h2>";
    echo "<hr>";

    try {
        echo "<h3>🔧 正在初始化 PHPMailer...</h3>";
        $mail = new PHPMailer(true);

        echo "<h3>⚙️ 配置 SMTP 设置...</h3>";
        $mail->isSMTP();
        $mail->Host       = 'smtp-mail.outlook.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'p]@:H6j^K$SDF9a-';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;
        
        // 启用最详细的调试输出
        $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        $mail->Debugoutput = function($str, $level) {
            echo "<div style='background: #f0f0f0; padding: 5px; margin: 2px; border-left: 3px solid #007cba;'>";
            echo "<strong>DEBUG $level:</strong> " . htmlspecialchars($str);
            echo "</div>";
        };

        // 超时设置
        $mail->Timeout = 30;
        $mail->SMTPKeepAlive = true;

        echo "<h3>📬 设置收发件人...</h3>";
        $mail->setFrom('<EMAIL>', 'VW Website Test');
        $mail->addAddress('<EMAIL>', 'Test Recipient');
        $mail->addReplyTo('<EMAIL>', 'Test Sender');

        echo "<h3>✉️ 设置邮件内容...</h3>";
        $mail->isHTML(true);
        $mail->Subject = '🧪 VW Architects - Mail Test ' . date('Y-m-d H:i:s');
        $mail->Body = '
        <html>
        <head><title>测试邮件</title></head>
        <body>
            <h2>📧 邮件发送测试</h2>
            <p><strong>测试时间:</strong> ' . date('Y-m-d H:i:s') . '</p>
            <p><strong>服务器:</strong> ' . $_SERVER['SERVER_NAME'] . '</p>
            <p><strong>IP地址:</strong> ' . $_SERVER['SERVER_ADDR'] . '</p>
            <p>如果您收到这封邮件，说明邮件系统工作正常！ ✅</p>
        </body>
        </html>';
        
        $mail->AltBody = '邮件发送测试 - 测试时间: ' . date('Y-m-d H:i:s');

        echo "<h3>🚀 开始发送邮件...</h3>";
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
        echo "<strong>注意:</strong> 下面将显示详细的SMTP通信过程...";
        echo "</div>";

        $result = $mail->send();
        
        if ($result) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>";
            echo "<h3>✅ 邮件发送成功！</h3>";
            echo "<p><strong>发送时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
            echo "<p><strong>收件人:</strong> <EMAIL></p>";
            echo "<p><strong>主题:</strong> " . $mail->Subject . "</p>";
            echo "</div>";
            
            echo "<div style='background: #bee5eb; color: #0c5460; padding: 10px; border: 1px solid #abdde5; margin: 10px 0;'>";
            echo "<h4>📋 检查事项:</h4>";
            echo "<ul>";
            echo "<li>✉️ 检查 <strong><EMAIL></strong> 的收件箱</li>";
            echo "<li>🗑️ 检查垃圾邮件/垃圾箱文件夹</li>";
            echo "<li>⏰ 邮件可能需要几分钟才能到达</li>";
            echo "<li>🔍 搜索主题: '". $mail->Subject . "'</li>";
            echo "</ul>";
            echo "</div>";
        }

    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>❌ 邮件发送失败！</h3>";
        echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>错误代码:</strong> " . $e->getCode() . "</p>";
        echo "<p><strong>错误文件:</strong> " . $e->getFile() . " (第 " . $e->getLine() . " 行)</p>";
        echo "</div>";
        
        echo "<div style='background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
        echo "<h4>🔧 可能的解决方案:</h4>";
        echo "<ul>";
        echo "<li>检查邮箱密码是否正确</li>";
        echo "<li>检查是否需要应用专用密码</li>";
        echo "<li>检查网络连接</li>";
        echo "<li>检查防火墙设置</li>";
        echo "</ul>";
        echo "</div>";
    }

    echo "<hr>";
    echo "<h3>📊 系统信息</h3>";
    echo "<ul>";
    echo "<li><strong>PHP版本:</strong> " . phpversion() . "</li>";
    echo "<li><strong>OpenSSL支持:</strong> " . (extension_loaded('openssl') ? '✅ 启用' : '❌ 未启用') . "</li>";
    echo "<li><strong>SMTP扩展:</strong> " . (extension_loaded('smtp') ? '✅ 启用' : '⚠️ 未安装') . "</li>";
    echo "<li><strong>时间:</strong> " . date('Y-m-d H:i:s T') . "</li>";
    echo "</ul>";
?> 