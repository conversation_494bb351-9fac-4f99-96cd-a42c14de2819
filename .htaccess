Options +SymLinksIfOwnerMatch -MultiViews
<IfModule mod_rewrite.c>
RewriteEngine On

#If your website is installed in a subfolder, change the line below to reflect the path to the subfolder.
#e.g. for http://www.example.com/subdomain1/subdomain2/ make it RewriteBase /subdomain1/subdomain2
RewriteBase /

#If you wish to use a custom 404 page, place a file named 404.php in your website's root and uncomment the line below.
#If your website is installed in a subfolder, change the line below to reflect the path to the subfolder.
#e.g. for http://www.example.com/subdomain1/subdomain2/ make it ErrorDocument 404 /subdomain1/subdomain2/404.php
#ErrorDocument 404 /404.php

#If your site begins with 'www', uncomment the following two lines
#RewriteCond %{HTTP_HOST} !^www\.
#RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]


#DO NOT EDIT BELOW THIS

RewriteRule ^projects/multiunit/index.php$ "projects/multiunit/" [R=301,L,QSA]
RewriteRule ^projects/masterplanning/index.php$ "projects/masterplanning/" [R=301,L,QSA]
RewriteRule ^projects/homes/index.php$ "projects/homes/" [R=301,L,QSA]
RewriteRule ^projects/heritage/index.php$ "projects/heritage/" [R=301,L,QSA]
RewriteRule ^projects/community/index.php$ "projects/community/" [R=301,L,QSA]
RewriteRule ^projects/commercial/index.php$ "projects/commercial/" [R=301,L,QSA]
RewriteRule ^team/index.php$ "team/" [R=301,L,QSA]
RewriteRule ^projects/index.php$ "projects/" [R=301,L,QSA]
RewriteRule ^journal/index.php$ "journal/" [R=301,L,QSA]
RewriteRule ^casestudy/index.php$ "casestudy/" [R=301,L,QSA]
RewriteRule ^index.php$ "" [R=301,L,QSA]

RewriteCond %{REQUEST_FILENAME} -d [OR]
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule . - [L]

#projects/multiunit/index.php
RewriteRule ^projects/multiunit/.*?([^\.\/]*)\.html$ projects/multiunit/?pname=$1 [L,QSA]
RewriteRule ^projects/multiunit/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/multiunit/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/multiunit/[^\.]*?([^/\.]*)/$ projects/multiunit/?fname=$1 [L,QSA]
RewriteRule ^projects/multiunit/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/masterplanning/index.php
RewriteRule ^projects/masterplanning/.*?([^\.\/]*)\.html$ projects/masterplanning/?pname=$1 [L,QSA]
RewriteRule ^projects/masterplanning/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/masterplanning/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/masterplanning/[^\.]*?([^/\.]*)/$ projects/masterplanning/?fname=$1 [L,QSA]
RewriteRule ^projects/masterplanning/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/homes/index.php
RewriteRule ^projects/homes/.*?([^\.\/]*)\.html$ projects/homes/?pname=$1 [L,QSA]
RewriteRule ^projects/homes/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/homes/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/homes/[^\.]*?([^/\.]*)/$ projects/homes/?fname=$1 [L,QSA]
RewriteRule ^projects/homes/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/heritage/index.php
RewriteRule ^projects/heritage/.*?([^\.\/]*)\.html$ projects/heritage/?pname=$1 [L,QSA]
RewriteRule ^projects/heritage/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/heritage/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/heritage/[^\.]*?([^/\.]*)/$ projects/heritage/?fname=$1 [L,QSA]
RewriteRule ^projects/heritage/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/community/index.php
RewriteRule ^projects/community/.*?([^\.\/]*)\.html$ projects/community/?pname=$1 [L,QSA]
RewriteRule ^projects/community/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/community/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/community/[^\.]*?([^/\.]*)/$ projects/community/?fname=$1 [L,QSA]
RewriteRule ^projects/community/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/commercial/index.php
RewriteRule ^projects/commercial/.*?([^\.\/]*)\.html$ projects/commercial/?pname=$1 [L,QSA]
RewriteRule ^projects/commercial/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/commercial/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/commercial/[^\.]*?([^/\.]*)/$ projects/commercial/?fname=$1 [L,QSA]
RewriteRule ^projects/commercial/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#team/index.php
RewriteRule ^team/.*?([^\.\/]*)\.html$ team/?pname=$1 [L,QSA]
RewriteRule ^team/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ team/?d=$1$2$3 [L,QSA]
RewriteRule ^team/[^\.]*?([^/\.]*)/$ team/?fname=$1 [L,QSA]
RewriteRule ^team/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#projects/index.php
RewriteRule ^projects/.*?([^\.\/]*)\.html$ projects/?pname=$1 [L,QSA]
RewriteRule ^projects/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ projects/?d=$1$2$3 [L,QSA]
RewriteRule ^projects/[^\.]*?([^/\.]*)/$ projects/?fname=$1 [L,QSA]
RewriteRule ^projects/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#journal/index.php
RewriteRule ^journal/.*?([^\.\/]*)\.html$ journal/?pname=$1 [L,QSA]
RewriteRule ^journal/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ journal/?d=$1$2$3 [L,QSA]
RewriteRule ^journal/[^\.]*?([^/\.]*)/$ journal/?fname=$1 [L,QSA]
RewriteRule ^journal/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#casestudy/index.php
RewriteRule ^casestudy/.*?([^\.\/]*)\.html$ casestudy/?pname=$1 [L,QSA]
RewriteRule ^casestudy/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ casestudy/?d=$1$2$3 [L,QSA]
RewriteRule ^casestudy/[^\.]*?([^/\.]*)/$ casestudy/?fname=$1 [L,QSA]
RewriteRule ^casestudy/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#global.php
RewriteRule ^global$ "$0/" [R=301,L,QSA]
RewriteRule ^global/$ global.php [L,QSA]
RewriteRule ^global/.*?([^\.\/]*)\.html$ global.php?pname=$1 [L,QSA]
RewriteRule ^global/([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ global.php?d=$1$2$3 [L,QSA]
RewriteRule ^global/[^\.]*?([^/\.]*)/$ global.php?fname=$1 [L,QSA]
RewriteRule ^global/[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

#index.php
RewriteRule ^.*?([^\.\/]*)\.html$ ?pname=$1 [L,QSA]
RewriteRule ^([1-2]\d{3})/(?:(0[1-9]|1[0-2])/(?:(0[1-9]|1[0-9]|2[0-9]|3[0-1])/)?)?$ ?d=$1$2$3 [L,QSA]
RewriteRule ^[^\.]*?([^/\.]*)/$ ?fname=$1 [L,QSA]
RewriteRule ^\w[^\.]*?([^/\.]*)$ "$0/" [R=301,L,QSA]

# Redirect from root to vwarchitects folder
RewriteCond %{REQUEST_URI} ^/$
RewriteRule ^(.*)$ vwarchitects/ [L,R=301]

# Handle all other requests within vwarchitects folder
RewriteCond %{REQUEST_URI} !^/vwarchitects/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ vwarchitects/$1 [L]
</IfModule>