<?php    
    header('Access-Control-Allow-Origin: *');
    header('Content-Type: application/json');

    use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\Exception;
    use PHPMailer\PHPMailer\SMTP;

    require 'PHPMailer/Exception.php';
    require 'PHPMailer/PHPMailer.php';
    require 'PHPMailer/SMTP.php';

    try {
        //Create an instance; passing `true` enables exceptions
        $mail = new PHPMailer(true);

        //Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp-mail.outlook.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'p]@:H6j^K$SDF9a-';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;
        
        // Enable verbose debug output (设置为2查看详细调试信息)
        $mail->SMTPDebug = 2;
        $mail->Debugoutput = 'html';

        // Timeout settings
        $mail->Timeout = 60;
        $mail->SMTPKeepAlive = true;

        //Recipients
        $mail->setFrom('<EMAIL>', 'VW Website Admin');
        $mail->addAddress('<EMAIL>', 'Recipient');

        // Validate POST data
        if (!isset($_POST["email"]) || empty($_POST["email"])) {
            throw new Exception('Email is required');
        }

        $firstName = filter_var($_POST["firstName"], FILTER_SANITIZE_STRING);
        $lastName = filter_var($_POST["lastName"], FILTER_SANITIZE_STRING);
        $email = filter_var($_POST["email"], FILTER_SANITIZE_EMAIL);
        $phoneNumber = filter_var($_POST["phoneNumber"], FILTER_SANITIZE_STRING);
        $message = filter_var($_POST["message"], FILTER_SANITIZE_STRING);

        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }

        $email_body = '
        <html>
            <head>
              <title>Contact Email</title>
            </head>
            
            <body>
              <p style="font-size: 18px; font-weight: bold;">You have received a new email inquiry from the website</p>
              <table width="100%" border-top="0.5" cellpadding="5" cellspacing="0" style="border-style: solid; border-width: 1px; border-color:#EAF2FA;">
                <tr bgcolor="#EAF2FA">
                  <td colspan="2">
                      <font style="font-size:12px">
                          <strong>First Name:</strong>
                      </font>
                   </td>
                </tr>
                <tr>
                    <td width="20">&nbsp;</td>
                    <td bgcolor="#FFFFFF">
                        '.$firstName.'
                    </td>
                </tr>
                <tr bgcolor="#EAF2FA">
                  <td colspan="2">
                      <font style="font-size:12px">
                          <strong>Last Name:</strong>
                      </font>
                   </td>
                </tr>
                <tr>
                    <td width="20">&nbsp;</td>
                    <td bgcolor="#FFFFFF">
                        '.$lastName.'
                    </td>
                </tr>
                <tr bgcolor="#EAF2FA">
                  <td colspan="2">
                      <font style="font-size:12px">
                          <strong>Email:</strong>
                      </font>
                   </td>
                </tr>
                <tr>
                    <td width="20">&nbsp;</td>
                    <td bgcolor="#FFFFFF">
                        '.$email.'
                    </td>
                </tr>
                <tr bgcolor="#EAF2FA">
                  <td colspan="2">
                      <font style="font-size:12px">
                          <strong>Phone Number:</strong>
                      </font>
                   </td>
                </tr>
                <tr>
                    <td width="20">&nbsp;</td>
                    <td bgcolor="#FFFFFF">
                        '.$phoneNumber.'
                    </td>
                </tr>
                <tr bgcolor="#EAF2FA">
                  <td colspan="2">
                      <font style="font-size:12px">
                          <strong>Message:</strong>
                      </font>
                   </td>
                </tr>
                <tr>
                    <td width="20">&nbsp;</td>
                    <td bgcolor="#FFFFFF">
                        '.$message.'
                    </td>
                </tr>
              </table>
            </body>
        </html>
        ';

        //Content
        $mail->isHTML(true);
        $mail->Subject = 'Contact form submission from website';
        $mail->Body = $email_body;

        $mail->send();
        echo json_encode(['success' => true, 'message' => 'Message has been sent!']);

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Mail Error: " . $e->getMessage());
        
        echo json_encode([
            'success' => false, 
            'message' => 'Message could not be sent. <NAME_EMAIL>',
            'debug' => $e->getMessage() // 在生产环境中应该移除这行
        ]);
    }
?>