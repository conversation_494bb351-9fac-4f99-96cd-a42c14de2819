.team__hero {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
}

.team__hero--image {
    height: 790px;
    max-height: 75vh;
    flex: 1 0 48%;
}

.team__hero--image img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: bottom;
    box-sizing: border-box;
}

.team__hero--copy {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 48%;
}

.team__hero--copy h3 {
    text-transform: unset;
}

.team__content--nav {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 50px;
    row-gap: 25px;
    grid-column: 1 / 13;
    grid-row: span 1;
}

.team__content {
    display: flex;
    flex-flow: column nowrap;
    row-gap: 50px;
}

.team__content--item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    row-gap: 25px;
    column-gap: 75px;
}

.team__content--image {
    height: 475px;
    width: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.team__content--copy {
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    flex: 1 0 48%;
}

.team__photo {
    margin-bottom: -7px;
}

.team__photo img {
    height: 100%;
    width: 100%;
    object-position: center;
    object-fit: contain;
}

.team__info {
    display: flex;
    flex-flow: row nowrap;
    column-gap: 75px;
    row-gap: 25px;
}

.team__info--header {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    flex: 1 1 18%;
}

.team__info--content {
    flex: 1 1 78%;
    display: flex;
    flex-flow: row wrap;
    column-gap: 50px;
    row-gap: 50px;
}

.team__info--contentHeader {
    margin: 0;
    flex: 1 0 100%;
}

.team__item {
    display: flex;
    flex-flow: column nowrap;
    row-gap: 5px;
    flex: 1 0 28%;
}

.team__item[data-item="header"] {
    row-gap: 25px;
    column-gap: 25px;
}

.team__item--copy[data-item="header"] {
    display: flex;
    flex-flow: column nowrap;
    row-gap: 25px;
}

.team__item h3, p {
    text-transform: unset;
    margin: 0;
}

@media screen and (max-width: 1366px) {
    .team__hero {
        flex-flow: column nowrap;
    }

    .team__hero--image {
        height: 445px;
        width: 100%;
        flex: unset;
    }

    .team__content--image {
        height: 300px;
    }
    
    .team__info {
        flex-flow: column nowrap;
    }

    .team__item[data-item="header"] {
        flex-flow: row wrap;
    }

    .team__info--header {
        flex-flow: row wrap;
        flex: 1;
    }

    .team__info--content {
        flex: 1;
        column-gap: 45px;
        row-gap: 45px;
    }
}

@media screen and (max-width: 1024px) {
    .team__content--nav {
        flex-flow: column nowrap;
    }
    .team__content--item {
        flex-flow: column nowrap;
    }

    .team__item {
        flex: 1 0 45%;
    }

    .team__wrapper {
        padding-bottom: 50px;
    }

    .team__content--image {
        height: 186px;
    }

    .team__info--content {
        column-gap: 25px;
        row-gap: 25px;
    }
}

@media screen and (max-width: 768px) {
    .team__hero--image {
        height: 289px;
    }
}