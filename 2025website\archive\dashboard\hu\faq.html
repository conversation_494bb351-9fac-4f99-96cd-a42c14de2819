<!doctype html>
<html lang="hu">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="hu hu_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/hu/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/hu/faq.html">Gyakori kérdések</a></li>
              <li class="item "><a href="/dashboard/hu/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>Gyakran ismételt kérdések</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>Hogyan telepítsem a XAMPP-ot Mac OS X rendszerre?</dt>
      <dd>
      <p>A XAMPP telepítéséhez tegye a következőt:</p>
      <ul>
        <li>Nyissa meg a DMG-képfájlt.</li>
        <li>Kattintson duplán a képfájlra a telepítési folyamat indításához.</li></ul>
      <p>Ennyi. A XAMPP most már telepítve van az /Applications/XAMPP könyvtár alá.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Hogyan indítsam el a XAMPP-ot?</dt>
      <dd>
      <p>A XAMPP indításához egyszerűen nyissa meg a XAMPP vezérlőt és indítsa el az Apache, MySQL és ProFTPD kiszolgálókat. A XAMPP vezérlő neve „manager-osx”.</p>      
      </dd>
      <dt>Hogyan állíthatom le a XAMPP-ot?</dt>
      <dd>
      <p>A XAMPP leállításához egyszerűen nyissa meg a XAMPP vezérlőt és állítsa le kiszolgálókat. A XAMPP vezérlő neve „manager-osx”.</p>      
      </dd>
      <dt>Hogyan ellenőrizhetem, hogy minden működik-e?</dt>
      <dd>
      <p>Gépelje be a következő URL-t a webböngészőbe:</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>A XAMPP készen áll a munkához?</dt>
      <dd><p>A XAMPP nem a produktív használatot jelenti, ez csak egy fejlesztői környezet. A XAMPP oly módon lett beállítva, hogy annyira nyitott legyen, amennyire csak lehetséges, hogy lehetővé tegyen a fejlesztőknek bármit, amit szeretnének. Ez nagyszerű egy fejlesztői környezethez, de produktív környezetben végzetes lehet.</p>
      <p>Itt egy lista a XAMPP hiányzó biztonsági dolgairól:</p>
      <ol>
        <li>A MySQL adminisztrátornak (root) nincs jelszava.</li>
        <li>A MySQL démon elérhető a hálózaton keresztül.</li>
        <li>A ProFTPD a „lampp” jelszót használja a „daemon” felhasználóhoz.</li>
      </ol>
      <p>A biztonsági gyengeségek nagy részének javításához egyszerűen adja ki a következő parancsot:</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>Ez elindít egy kis biztonsági ellenőrzést és biztonságossá teszi a XAMPP telepítését.</p></dd>

      <dt>Mit jelentenek azok a hibaüzenetek, amelyeket a XAMPP indításakor látok?</dt>
      <dd>
        <p>Számos hibaüzenetet kaphat a XAMPP indításakor:</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>A LAMPP indítási parancsfájl nem tudta elindítani a XAMPP-Apache programot, mert egy Apache példány már fut. A XAMPP megfelelő indításához először le kell állítania ezt a démont.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>Ez legtöbbször hasonló okok miatt történik mint a fenti hiba. A LAMPP indítási parancsfájl talált egy MySQL démont, amely már fut a rendszeren. A LAMPP megfelelő indításához először le kell állítania ezt a démont.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>Ez a hiba több ok miatt állhat fenn. Az Apache ezt a hibát számos körülmény miatt jelenítheti meg. A pontos ok megtalálásához egy kis kutatást kell tennünk:</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>Ha hibaüzenetet kapott, látogassa meg a <a href="/community.html">közösségi oldalainkat</a> a segítségért.</p>
      </dd>

      <dt>Hogyan tehetem a XAMPP telepítésemet még biztonságosabbá?</dt>
      <dd>
        <p>Az alapértelmezett telepítésben a XAMPP nem rendelkezik beállított jelszóval és nem javasolt a XAMPP futtatása ezzel a beállítással mások számára elérhető módon.</p>
        <p>Egyszerűen gépelje be a következő parancsot (rendszergazdaként) egy egyszerű biztonsági ellenőrzés indításához:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>Most a következő párbeszédablakot kellene látnia a képernyőjén:</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) Egy jelszó beállításával fogja védeni a XAMPP bemutató oldalakat (http://localhost/xampp/) ennek a jelszónak a használatával. A felhasználónév a „lampp”!</p>
        <p>A parancs kiadása után a XAMPP telepítése sokkal biztonságosabb lesz.</p>
      </dd>

      <dt>Hogyan aktiválhatom az OCI8/Oracle PHP kiterjesztést?</dt>
      <dd>
        <p>Az OCI8/Oracle PHP kiterjesztés aktiváláshoz futtassa le a következő parancsot:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>A következő párbeszédablak fog elindulni:</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>A kiterjesztés most már aktív.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
        <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
        <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
        <p>To enable remote access to phpMyAdmin, follow these steps:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>Within this file, find the lines below. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Then replace 'Require local' with 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Restart the Apache server using the XAMPP control panel.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>Hogyan tudok biztonsági mentést és visszaállítást készíteni a XAMPP rendszeremről?</dt>
      <dd>
        <p><strong>Figyelem:</strong> A biztonsági mentés és visszaállítás funkció még fejlesztés alatt van és lehet, hogy nem megfelelően működik.</p>
        <p>A biztonsági mentés létrehozásához írja be ezt:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>vagy</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>Ahol a „secret” a MySQL root jelszava. Ez a parancs a következő kimenetet fogja eredményezni:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>A (fenti példában lévő) /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh fájl tartalmazza a biztonsági mentés adatait. Tegye ezt a fájlt biztonságos helyre.</p>

        <p>Az új gépen szüksége lesz ugyanarra a XAMPP verzióra, mint amelyet az eredeti/forrás gépen használt.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>Valami ehhez hasonlót kell látnia:</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>Ennyi. Ne feledje, ez egy béta állapotú szolgáltatás.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright © Apache Friends, 2022.</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Adatvédelmi irányelv</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN által nyújtott
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
