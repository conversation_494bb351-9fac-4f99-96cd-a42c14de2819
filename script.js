const navToggle = document.querySelectorAll('.nav__toggle');
const contactBtn = document.querySelectorAll('.contactBtn');
const navMenu = document.querySelector('.nav__menu');
const nav = document.querySelector('.nav');

if (navToggle && navMenu) {
    navToggle.forEach(el => el.addEventListener('click', () => {
        navMenu.classList.toggle('active');
    }));
}

if (contactBtn.length > 0) contactBtn.forEach(el => el.addEventListener('click', () => navMenu.classList.remove('active')))

if (nav) {
    window.addEventListener('scroll', () => {
        if (scrollY > 50) {
            nav.classList.add('fixed');
        } else {
            nav.classList.remove('fixed'); 
        }
    })
}