<cms:pages masterpage='index.php'>
    <section class="contact contactPopup">
        <div class="cw-xl sp contact__container" data-aos="fade">
            <div class="contactPopup__close">
                <img src="../assets/svg/menuOpen.svg" />
            </div>

            <h2><cms:show homecontact__header /></h2>
            <p><cms:show homecontact__copy /></p>

            <?php include('../partials/contactForm.php') ?>
        </div>
    </section>
</cms:pages>

<script>
    const docBody = document.querySelector('body')
    const contactPopup = document.querySelector('.contactPopup');
    const contactPopupClose = document.querySelectorAll('.contactPopup__close');
    const contactPopupBtns = document.querySelectorAll('.btnContactPopup');

    contactPopupBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            docBody.classList.add('noscroll');
            contactPopup.classList.add('active');
        });
    });

    contactPopupClose.forEach(close => {
        close.addEventListener('click', () => {
            docBody.classList.remove('noscroll');
            contactPopup.classList.remove('active');
        });
    });
</script>