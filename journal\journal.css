.journal__hero {
    width: 100%;
}

.journal__hero--image img {
    height: 790px;
    max-height: 75vh;
    width: 100%;
    object-fit: cover;
    object-position: center;
    box-sizing: border-box;
}

.journal__hero--copy {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.journal__hero--copy h2 {
    margin: 0;
}

.journal__content--nav {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    column-gap: 50px;
    row-gap: 25px;
    grid-column: 1 / 13;
    grid-row: span 1;
}

.journal__category {
    display: none;
}

.journal__category.active {
    display: block;
}

.journal__content {
    display: flex;
    flex-flow: column nowrap;
    row-gap: 50px;
}

.journal__content--item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    row-gap: 25px;
    column-gap: 75px;
    width: 100%;
}

.journal__content--image {
    height: 475px;
    width: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.journal__content--copy {
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    flex: 1 0 48%;
}

@media screen and (max-width: 1366px) {
    .journal__hero--image img {
        height: 445px;
    }

    .journal__content--image {
        height: 300px;
    }
    
}

@media screen and (max-width: 1024px) {
    .journal__content--nav {
        flex-flow: column nowrap;
    }
    .journal__content--item {
        flex-flow: column nowrap;
    }

    .journal__wrapper {
        padding-bottom: 50px;
    }

    .journal__content--image {
        height: 186px;
    }
}

@media screen and (max-width: 768px) {
    .journal__hero--image img {
        height: 289px;
    }
}