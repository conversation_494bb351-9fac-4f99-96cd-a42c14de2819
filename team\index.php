<?php require_once( '../admin/cms.php' ); ?>

<cms:template title='Team Page'>
    <cms:editable name="teamhero__group" label="Hero section" type="group" order="1" />
    <cms:editable name="team__image" label="Team header image" type="image" group="teamhero__group" />
    <cms:editable name="team__header" label="Team header text" type="text" group="teamhero__group" />
    <cms:editable name="team__subtitle" label="Team subtitle" type="text" group="teamhero__group" />
    <cms:editable name="team__copy" label="Team copy" group="teamhero__group" type="richtext" />

    <cms:editable name="teamstudio__group" label="Studio section" type="group" order="2" />
    <cms:editable name="teamstudio__header" label="Team studio text" type="text" group="teamstudio__group" />
    <cms:editable name="teamstudio__copy" label="Team studio text" group="teamstudio__group" type="richtext" />


    <cms:editable name="mainmember__group" label="Main member group" type="group" order="3"/>
    <cms:editable name="main__name" label="Team member name" type="text" group="mainmember__group" />
    <cms:editable name="main__position" label="Team member position" type="text" group="mainmember__group" />
    <cms:editable name="main__description" label="Team member description" type="text" group="mainmember__group" />
    <cms:editable name="main__image" label="Team member image" type="image" group="mainmember__group" />
    <cms:editable name="main__email" label="Team member email" type="text" group="mainmember__group" />


    <cms:repeatable order="4" name="teammembers">
        <cms:editable name="teammember__name" label="Team member name" type="text" />
        <cms:editable name="teammember__position" label="Team member position" type="text" />
        <cms:editable name="teammember__email" label="Team member email" type="text" />
    </cms:repeatable>

    <cms:editable type="image" order="5" name="teamfooterimage" label="Footer image" />
</cms:template>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team | VW Architects</title>

    <?php include('../partials/headscripts.php') ?>

    <!-- CSS -->
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="team.css">
</head>
<body>
    <div class="container" data-page="team">
        <?php include('../partials/nav.php') ?>
  
        <section class="team__hero">
            <div class="team__hero--image" data-aos="fade">
                <img src="<cms:show team__image />" />
            </div>

            <div class="cw-xl sp team__hero--copy">
                <h2 data-aos="fade" data-aos-delay="50"><cms:show team__header /></h2>
                <h3 data-aos="fade" data-aos-delay="100"><cms:show team__subtitle /></h3>
                <div data-aos="fade" data-aos-delay="150"><cms:show team__copy /></div>
            </div>
        </section>

        <section class="cw-xl sp team__studio">
            <h2 data-aos="fade" data-aos-delay=""><cms:show teamstudio__header /></h2>
            <div data-aos="fade" data-aos-delay="50"><cms:show teamstudio__copy /></div>
        </section>

        <section class="team__photo" data-aos="fade">
            <img src="<cms:show teamfooterimage />" alt="">
        </section>

        <section class="team__members" data-color="grey">
            <div class="sp cw-xl team__info" data-aos="fade" data-aos-delay="25">
                <div class="team__info--header">
                    <div class="team__item" data-item="header">
                        <div class="team__item--image" data-item="header" data-aos="fade" data-aos-delay="">
                            <img class="teamHeaderImage" src="<cms:show main__image />" />
                        </div>
        
                        <cms:show_repeatable 'teammembers'>
                            <cms:if k_count = '1'>
                                <div class="team__item--copy" data-item="header" data-aos="fade" data-aos-delay="10">
                                    <h3 id="teamHeaderName" style="color: #A15D3C"><cms:show main__name /></h3>
                                    <p id="teamHeaderPosition"><cms:show main__position /></p>
                                    <p id="teamHeaderDescription"><cms:show main__description /></p>
                                    <button class="btn btnContactPopup">Contact Us</button>
                                </div>
                            </cms:if>
                        </cms:show_repeatable>
                    </div>
                </div>
    
                <div class="team__info--content">
                    <h3 class="team__info--contentHeader" data-aos="fade" data-aos-delay="">Our Team</h3>

                    <cms:show_repeatable 'teammembers'>
                        <div class="team__item"
                            data-aos="fade"
                            data-aos-delay="<cms:show k_count />00"
                        >
                            <h3 data-aos="fade" data-aos-delay="" style="color: #A15D3C"><cms:show teammember__name /></h3>
                            <p data-aos="fade" data-aos-delay="25"><cms:show teammember__position /></p>
                            <p data-aos="fade" data-aos-delay="50"><cms:show teammember__email /></p>
                        </div>
                    </cms:show_repeatable>
                </div>
            </div>
        </section>

        <?php include('../partials/footer.php') ?>
    </div>

    <?php include('../partials/contactPopup.php') ?>

    <script src="../script.js"></script>

    <script>
        if (document.querySelector('.instagram__swiper')) {
            new Swiper('.instagram__swiper', {
                autoplay: {
                    delay: 5000
                },
                slidesPerView: "auto",
                spaceBetween: 30,
                centeredSlides: true,
                loop: true,
            });
        }
    </script>
    
    <?php include('../partials/navActive.php') ?>
</body>
</html>

<?php COUCH::invoke(); ?>