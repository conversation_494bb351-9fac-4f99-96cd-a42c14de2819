<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Backup and Restore MySQL/MariaDB Databases</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_backup-restore-mysql">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/backup-restore-mysql.pdf">          Download PDF
          <span>backup-restore-mysql.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
        <aside>
          <h3>Contents</h3>
          <ol class="sections">
              <li><a href="/dashboard/docs/backup-restore-mysql.html#using_phpmyadmin">Using phpMyAdmin</a></li>
              <li><a href="/dashboard/docs/backup-restore-mysql.html#using_command_line_tools">Using Command-Line Tools</a></li>
          </ol>
        </aside>
      <h1>Backup and Restore MySQL/MariaDB Databases</h1>
<div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>As you use XAMPP, you might find that you need to backup or restore a MySQL or MariaDB database. There are two easy ways to do this with XAMPP: using the browser-based phpMyAdmin tool or using MySQL/MariaDB&#8217;s command-line tools. This guide describes both options.</p>
</div>
<div class="paragraph">
<p>This guide assumes that you already have a PHP application that uses a MySQL/MariaDB database deployed on XAMPP. The example application used in this guide is WordPress, although the steps outlined below will work for any application.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Remember that you can install WordPress quickly using the <a href="https://bitnami.com/stack/xampp#wordpress">Bitnami WordPress module for XAMPP</a>.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_using_phpmyadmin">Using phpMyAdmin</h2>
<div class="sectionbody">
<div class="paragraph">
<p>XAMPP includes <a href="http://www.phpmyadmin.net/">phpMyAdmin</a>, an open-source, browser-based tool for managing MySQL/MariaDB database servers. To backup a MySQL/MariaDB database using phpMyAdmin, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Browse to <a href="http://localhost/phpMyAdmin" class="bare">http://localhost/phpMyAdmin</a> or <a href="http://127.0.0.1/phpMyAdmin" class="bare">http://127.0.0.1/phpMyAdmin</a>. If required, log in using your database access credentials. On a fresh XAMPP installation without any changes, you can log in as <em>root</em> with a blank password.</p>
</li>
<li>
<p>Select the database to be backed up from the list in the left navigation pane. In this case, we&#8217;re backing up the WordPress database, which is named <em>bitnami_wordpress</em>.</p>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image1.png" alt="image1">
</div>
</div>
</li>
<li>
<p>Select the "Export" command in the top navigation bar.</p>
</li>
<li>
<p>On the resulting page, select "Quick" as the export method and "SQL" as the output format. Click "Go" to proceed.</p>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image2.png" alt="image2">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>phpMyAdmin will export the database to a text file as a series of SQL statements. Once done, the browser will prompt you to download it to the desktop. This text file is your backup, so store it safely!</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image3.png" alt="image3">
</div>
</div>
<div class="paragraph">
<p>At a later point, you may wish to restore the database. To restore the data to a fresh MySQL/MariaDB database using phpMyAdmin, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Browse to <a href="http://localhost/phpMyAdmin" class="bare">http://localhost/phpMyAdmin</a> or <a href="http://127.0.0.1/phpMyAdmin" class="bare">http://127.0.0.1/phpMyAdmin</a>. If required, log in using your database access credentials.</p>
</li>
<li>
<p>Select the "New" command in the left navigation pane.</p>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image4.png" alt="image4">
</div>
</div>
</li>
<li>
<p>On the resulting page, enter a name for the new database (in this case, <em>myblog</em>). Select the collation "utf8_general_ci". Click "Create" to create the database.</p>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image5.png" alt="image5">
</div>
</div>
<div class="paragraph">
<p>Once the database has been created, it will appear in the left navigation pane.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image6.png" alt="image6">
</div>
</div>
</li>
<li>
<p>Select the new database from the left navigation pane. In the resulting page, select the "Import" command in the top navigation bar.</p>
</li>
<li>
<p>Click the "Browse&#8230;&#8203;" button and select the backup file created earlier. Click "Go" to proceed.</p>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image7.png" alt="image7">
</div>
</div>
<div class="paragraph">
<p>phpMyAdmin will import the data from the backup file. Once complete, you will see a screen indicating the status of the import.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image8.png" alt="image8">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>If you browse the contents of the database, you should now see your original data.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image9.png" alt="image9">
</div>
</div>
<div class="paragraph">
<p>You should now update your application&#8217;s configuration and point it to your newly-created database. For example, with the Bitnami WordPress module, you will need to edit the <em>wp-config.php</em> file in the <em>apps\wordpress\htdocs\</em> subdirectory of your XAMPP installation directory. Within this file, you will need to update the <em>DB_NAME</em> constant to use the new database name.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image10.png" alt="image10">
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
By default, PHP (and therefore phpMyAdmin) is configured to only accept uploads up to 128 MB in size. If your database is larger than this, you need to modify the PHP configuration file to accept a larger upload size. To do this, edit the <em>php.ini</em> file in the <em>etc\</em> subdirectory of your XAMPP installation directory and increase the values of the <em>post_max_size</em> and <em>upload_max_filesize</em> variables. Save your changes and restart Apache for the changes to take effect.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_using_command_line_tools">Using Command-Line Tools</h2>
<div class="sectionbody">
<div class="paragraph">
<p>MySQL and MariaDB include two command-line tools that you can use to quickly backup and restore databases. The <em>mysqldump</em> tool is used to export the contents of a database to a text file, while the <em>mysql</em> client can be used to import data from a text file into a MySQL/MariaDB database.</p>
</div>
<div class="paragraph">
<p>To backup a MySQL/MariaDB database from the command line, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Ensure that the MySQL/MariaDB server is running.</p>
</li>
<li>
<p>Open a new terminal window.</p>
</li>
<li>
<p>Use the command below to export the contents of the selected database. In this example, we&#8217;re backing up the WordPress database, which is named <em>bitnami_wordpress</em>, to a file named <em>bitnami_wordpress.sql</em>. This text file is your backup, so store it safely!</p>
<div class="literalblock">
<div class="content">
<pre>/Applications/XAMPP/bin/mysqldump --user=root --password="" bitnami_wordpress &gt; bitnami_wordpress.sql</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image11.png" alt="image11">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>At a later point, you may wish to restore the database. To restore the data to a fresh MySQL/MariaDB database from the command line, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Ensure that the MySQL/MariaDB server is running.</p>
</li>
<li>
<p>Open a new terminal window.</p>
</li>
<li>
<p>Use the <em>mysql</em> client to create a new, empty database to hold your data. In this example, the new database is named <em>myblog</em>.</p>
<div class="literalblock">
<div class="content">
<pre>/Applications/XAMPP/bin/mysql --user=root --password="" -e "CREATE DATABASE myblog"</pre>
</div>
</div>
<div class="paragraph">
<p>Remember to use the correct database access credentials in the command. On a fresh XAMPP installation without any changes, you can usually log in as root with a blank password.</p>
</div>
</li>
<li>
<p>Use the <em>mysql</em> client to import the contents of the backup file into the new database.</p>
<div class="literalblock">
<div class="content">
<pre>/Applications/XAMPP/mysql --user=root --password="" --database=myblog &lt; bitnami_wordpress.sql</pre>
</div>
</div>
<div class="paragraph">
<p>The command-line client will now import the data from the backup file.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image12.png" alt="image12">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>If you browse the contents of the database, you should now see your original data.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image13.png" alt="image13">
</div>
</div>
<div class="paragraph">
<p>You should now update your application&#8217;s configuration and point it to your newly-created database. For example, with the Bitnami WordPress module, you will need to edit the <em>wp-config.php</em> file in the <em>apps/wordpress/htdocs/</em> subdirectory of your XAMPP installation directory. Within this file, you will need to update the <em>DB_NAME</em> constant to use the new database name.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/backup-restore-mysql/image14.png" alt="image14">
</div>
</div>
</div>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
