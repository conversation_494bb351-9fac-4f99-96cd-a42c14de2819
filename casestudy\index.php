<?php 
    require_once( '../admin/cms.php' );
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
?>

<cms:template title="Case Studies" clonable="1">
    <cms:editable type="group" name="casestudypreview__group" label="Case Study /planning page preview" />
    <cms:editable name="casestudy__header" label="Case Study header" type="text" group="casestudypreview__group" />
    <cms:editable name="casestudy__previewtext" label="Case Study preview text" type="text"group="casestudypreview__group" />
    <cms:editable name="casestudy__previewimagelg" label="Case Study large preview image" type="image" group="casestudypreview__group" />
    <cms:editable name="casestudy__previewimagesm" label="Case Study small preview image" type="image" group="casestudypreview__group" />

    <cms:editable type="group" name="casestudycontent__group" label="Case Study content" />
    <cms:editable name="casestudy__sketch" label="Case Study sketch" type="image"group="casestudycontent__group" order="1" />
    <cms:editable name="casestudy__headercopy" label="Case Study header copy" type="richtext"group="casestudycontent__group" order="2" />
    <cms:repeatable name="casestudy__gallery" label="Case Study gallery" group="casestudycontent__group" order="3">
        <cms:editable name="casestudy__galleryimage" label="Case Study gallery image" type="image" />
        <cms:editable name="casestudy__watermarkimage" label="Case Study watermark image" type="image" />
    </cms:repeatable>


    <cms:editable name="casestudy__displaycollaborators"
        order="4"
        label="Display collaborators?"
        desc="Display collaborators?"
        opt_values="Yes | No"
        opt_selected="No"
        type='dropdown'
        group="casestudycontent__group"
    />
    <cms:repeatable name="casestudy__collaborators" label="Case Study collaborators" group="casestudycontent__group" order="5">
        <cms:editable name="casestudy__collaborator" label="Case Study gallery collaborator" type="text" />
    </cms:repeatable>
    <cms:editable name="casestudy__content" label="Case Study content" type="richtext"group="casestudycontent__group" order="6" />
    <cms:editable name="casestudy__parallax" label="Case Study parallax" type="image" group="casestudycontent__group" order="7" />
    <cms:editable name="casestudy__testimonialheader" label="Case Study testimonial header" type="text"group="casestudycontent__group" order="8" />
    <cms:editable name="casestudy__testimonialcopy" label="Case Study testimonial copy" type="text" group="casestudycontent__group" order="9" />

    
    <cms:folder name="homes" title="Homes Case Studies" />
    <cms:folder name="commercial" title="Commercial Case Studies" />
    <cms:folder name="heritage" title="Heritage Case Studies" />
    <cms:folder name="community" title="Community Case Studies" />
    <cms:folder name="multiunit" title="Multi Unit Case Studies" />
    <cms:folder name="masterplanning" title="Masterplanning Case Studies" />
</cms:template>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Study | VW Architects</title>
</head>
<body>
</body>
</html>
<?php COUCH::invoke(); ?>